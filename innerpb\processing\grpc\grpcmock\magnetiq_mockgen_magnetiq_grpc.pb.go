// Code generated by MockGen. DO NOT EDIT.
// Source: magnetiq_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockMagnetiqClient is a mock of MagnetiqClient interface.
type MockMagnetiqClient struct {
	ctrl     *gomock.Controller
	recorder *MockMagnetiqClientMockRecorder
}

// MockMagnetiqClientMockRecorder is the mock recorder for MockMagnetiqClient.
type MockMagnetiqClientMockRecorder struct {
	mock *MockMagnetiqClient
}

// NewMockMagnetiqClient creates a new mock instance.
func NewMockMagnetiqClient(ctrl *gomock.Controller) *MockMagnetiqClient {
	mock := &MockMagnetiqClient{ctrl: ctrl}
	mock.recorder = &MockMagnetiqClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMagnetiqClient) EXPECT() *MockMagnetiqClientMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockMagnetiqClient) ApplePay(ctx context.Context, in *grpc.ApplePayRequestData, opts ...grpc0.CallOption) (*grpc.ApplePayResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplePay", varargs...)
	ret0, _ := ret[0].(*grpc.ApplePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockMagnetiqClientMockRecorder) ApplePay(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockMagnetiqClient)(nil).ApplePay), varargs...)
}

// Cancel mocks base method.
func (m *MockMagnetiqClient) Cancel(ctx context.Context, in *grpc.CancelRequest, opts ...grpc0.CallOption) (*grpc.CancelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Cancel", varargs...)
	ret0, _ := ret[0].(*grpc.CancelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Cancel indicates an expected call of Cancel.
func (mr *MockMagnetiqClientMockRecorder) Cancel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockMagnetiqClient)(nil).Cancel), varargs...)
}

// Charge mocks base method.
func (m *MockMagnetiqClient) Charge(ctx context.Context, in *grpc.ChargeRequest, opts ...grpc0.CallOption) (*grpc.ChargeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Charge", varargs...)
	ret0, _ := ret[0].(*grpc.ChargeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Charge indicates an expected call of Charge.
func (mr *MockMagnetiqClientMockRecorder) Charge(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockMagnetiqClient)(nil).Charge), varargs...)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockMagnetiqClient) GetAcquirerIdentifier(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.GetAcquirerIdentifierResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", varargs...)
	ret0, _ := ret[0].(*grpc.GetAcquirerIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockMagnetiqClientMockRecorder) GetAcquirerIdentifier(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockMagnetiqClient)(nil).GetAcquirerIdentifier), varargs...)
}

// GetBankTransactionStatus mocks base method.
func (m *MockMagnetiqClient) GetBankTransactionStatus(ctx context.Context, in *grpc.BankTransactionStatusRequest, opts ...grpc0.CallOption) (*grpc.BankTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", varargs...)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockMagnetiqClientMockRecorder) GetBankTransactionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockMagnetiqClient)(nil).GetBankTransactionStatus), varargs...)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockMagnetiqClient) GetBankTransactionStatusUnformated(ctx context.Context, in *grpc.BankTransactionStatusUnformatedRequest, opts ...grpc0.CallOption) (*grpc.BankTransactionStatusUnformatedResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", varargs...)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusUnformatedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockMagnetiqClientMockRecorder) GetBankTransactionStatusUnformated(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockMagnetiqClient)(nil).GetBankTransactionStatusUnformated), varargs...)
}

// GooglePay mocks base method.
func (m *MockMagnetiqClient) GooglePay(ctx context.Context, in *grpc.GooglePayRequestData, opts ...grpc0.CallOption) (*grpc.GooglePayResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GooglePay", varargs...)
	ret0, _ := ret[0].(*grpc.GooglePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockMagnetiqClientMockRecorder) GooglePay(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockMagnetiqClient)(nil).GooglePay), varargs...)
}

// MakeToken mocks base method.
func (m *MockMagnetiqClient) MakeToken(ctx context.Context, in *grpc.PayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeToken", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockMagnetiqClientMockRecorder) MakeToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockMagnetiqClient)(nil).MakeToken), varargs...)
}

// OneClickPayIn mocks base method.
func (m *MockMagnetiqClient) OneClickPayIn(ctx context.Context, in *grpc.OneClickPayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OneClickPayIn", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockMagnetiqClientMockRecorder) OneClickPayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockMagnetiqClient)(nil).OneClickPayIn), varargs...)
}

// PayIn mocks base method.
func (m *MockMagnetiqClient) PayIn(ctx context.Context, in *grpc.PayInRequestData, opts ...grpc0.CallOption) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayIn", varargs...)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayIn indicates an expected call of PayIn.
func (mr *MockMagnetiqClientMockRecorder) PayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockMagnetiqClient)(nil).PayIn), varargs...)
}

// PayOut mocks base method.
func (m *MockMagnetiqClient) PayOut(ctx context.Context, in *grpc.PayOutRequestData, opts ...grpc0.CallOption) (*grpc.PayOutResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayOut", varargs...)
	ret0, _ := ret[0].(*grpc.PayOutResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOut indicates an expected call of PayOut.
func (mr *MockMagnetiqClientMockRecorder) PayOut(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockMagnetiqClient)(nil).PayOut), varargs...)
}

// PayOutByPhone mocks base method.
func (m *MockMagnetiqClient) PayOutByPhone(ctx context.Context, in *grpc.PayOutByPhoneRequestData, opts ...grpc0.CallOption) (*grpc.PayOutResponseByPhoneData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PayOutByPhone", varargs...)
	ret0, _ := ret[0].(*grpc.PayOutResponseByPhoneData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockMagnetiqClientMockRecorder) PayOutByPhone(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockMagnetiqClient)(nil).PayOutByPhone), varargs...)
}

// Refund mocks base method.
func (m *MockMagnetiqClient) Refund(ctx context.Context, in *grpc.RefundRequest, opts ...grpc0.CallOption) (*grpc.RefundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Refund", varargs...)
	ret0, _ := ret[0].(*grpc.RefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Refund indicates an expected call of Refund.
func (mr *MockMagnetiqClientMockRecorder) Refund(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockMagnetiqClient)(nil).Refund), varargs...)
}

// ResolveVisaAlias mocks base method.
func (m *MockMagnetiqClient) ResolveVisaAlias(ctx context.Context, in *grpc.ResolveVisaAliasRequest, opts ...grpc0.CallOption) (*grpc.ResolveVisaAliasResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResolveVisaAlias", varargs...)
	ret0, _ := ret[0].(*grpc.ResolveVisaAliasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockMagnetiqClientMockRecorder) ResolveVisaAlias(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockMagnetiqClient)(nil).ResolveVisaAlias), varargs...)
}

// ThreeDSConfirm mocks base method.
func (m *MockMagnetiqClient) ThreeDSConfirm(ctx context.Context, in *grpc.ThreeDSRequestData, opts ...grpc0.CallOption) (*grpc.ThreeDSResponseData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ThreeDSConfirm", varargs...)
	ret0, _ := ret[0].(*grpc.ThreeDSResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockMagnetiqClientMockRecorder) ThreeDSConfirm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockMagnetiqClient)(nil).ThreeDSConfirm), varargs...)
}

// ThreeDSResume mocks base method.
func (m *MockMagnetiqClient) ThreeDSResume(ctx context.Context, in *grpc.ThreeDSResumeRequest, opts ...grpc0.CallOption) (*grpc.ThreeDSResumeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ThreeDSResume", varargs...)
	ret0, _ := ret[0].(*grpc.ThreeDSResumeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockMagnetiqClientMockRecorder) ThreeDSResume(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockMagnetiqClient)(nil).ThreeDSResume), varargs...)
}

// TwoStagePayIn mocks base method.
func (m *MockMagnetiqClient) TwoStagePayIn(ctx context.Context, in *grpc.TwoStagePayInRequest, opts ...grpc0.CallOption) (*grpc.TwoStagePayInResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TwoStagePayIn", varargs...)
	ret0, _ := ret[0].(*grpc.TwoStagePayInResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockMagnetiqClientMockRecorder) TwoStagePayIn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockMagnetiqClient)(nil).TwoStagePayIn), varargs...)
}

// MockMagnetiqServer is a mock of MagnetiqServer interface.
type MockMagnetiqServer struct {
	ctrl     *gomock.Controller
	recorder *MockMagnetiqServerMockRecorder
}

// MockMagnetiqServerMockRecorder is the mock recorder for MockMagnetiqServer.
type MockMagnetiqServerMockRecorder struct {
	mock *MockMagnetiqServer
}

// NewMockMagnetiqServer creates a new mock instance.
func NewMockMagnetiqServer(ctrl *gomock.Controller) *MockMagnetiqServer {
	mock := &MockMagnetiqServer{ctrl: ctrl}
	mock.recorder = &MockMagnetiqServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMagnetiqServer) EXPECT() *MockMagnetiqServerMockRecorder {
	return m.recorder
}

// ApplePay mocks base method.
func (m *MockMagnetiqServer) ApplePay(arg0 context.Context, arg1 *grpc.ApplePayRequestData) (*grpc.ApplePayResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplePay", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ApplePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplePay indicates an expected call of ApplePay.
func (mr *MockMagnetiqServerMockRecorder) ApplePay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplePay", reflect.TypeOf((*MockMagnetiqServer)(nil).ApplePay), arg0, arg1)
}

// Cancel mocks base method.
func (m *MockMagnetiqServer) Cancel(arg0 context.Context, arg1 *grpc.CancelRequest) (*grpc.CancelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", arg0, arg1)
	ret0, _ := ret[0].(*grpc.CancelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Cancel indicates an expected call of Cancel.
func (mr *MockMagnetiqServerMockRecorder) Cancel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockMagnetiqServer)(nil).Cancel), arg0, arg1)
}

// Charge mocks base method.
func (m *MockMagnetiqServer) Charge(arg0 context.Context, arg1 *grpc.ChargeRequest) (*grpc.ChargeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Charge", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ChargeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Charge indicates an expected call of Charge.
func (mr *MockMagnetiqServerMockRecorder) Charge(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Charge", reflect.TypeOf((*MockMagnetiqServer)(nil).Charge), arg0, arg1)
}

// GetAcquirerIdentifier mocks base method.
func (m *MockMagnetiqServer) GetAcquirerIdentifier(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.GetAcquirerIdentifierResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAcquirerIdentifier", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetAcquirerIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAcquirerIdentifier indicates an expected call of GetAcquirerIdentifier.
func (mr *MockMagnetiqServerMockRecorder) GetAcquirerIdentifier(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAcquirerIdentifier", reflect.TypeOf((*MockMagnetiqServer)(nil).GetAcquirerIdentifier), arg0, arg1)
}

// GetBankTransactionStatus mocks base method.
func (m *MockMagnetiqServer) GetBankTransactionStatus(arg0 context.Context, arg1 *grpc.BankTransactionStatusRequest) (*grpc.BankTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatus", arg0, arg1)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatus indicates an expected call of GetBankTransactionStatus.
func (mr *MockMagnetiqServerMockRecorder) GetBankTransactionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatus", reflect.TypeOf((*MockMagnetiqServer)(nil).GetBankTransactionStatus), arg0, arg1)
}

// GetBankTransactionStatusUnformated mocks base method.
func (m *MockMagnetiqServer) GetBankTransactionStatusUnformated(arg0 context.Context, arg1 *grpc.BankTransactionStatusUnformatedRequest) (*grpc.BankTransactionStatusUnformatedResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankTransactionStatusUnformated", arg0, arg1)
	ret0, _ := ret[0].(*grpc.BankTransactionStatusUnformatedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankTransactionStatusUnformated indicates an expected call of GetBankTransactionStatusUnformated.
func (mr *MockMagnetiqServerMockRecorder) GetBankTransactionStatusUnformated(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankTransactionStatusUnformated", reflect.TypeOf((*MockMagnetiqServer)(nil).GetBankTransactionStatusUnformated), arg0, arg1)
}

// GooglePay mocks base method.
func (m *MockMagnetiqServer) GooglePay(arg0 context.Context, arg1 *grpc.GooglePayRequestData) (*grpc.GooglePayResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GooglePay", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GooglePayResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GooglePay indicates an expected call of GooglePay.
func (mr *MockMagnetiqServerMockRecorder) GooglePay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GooglePay", reflect.TypeOf((*MockMagnetiqServer)(nil).GooglePay), arg0, arg1)
}

// MakeToken mocks base method.
func (m *MockMagnetiqServer) MakeToken(arg0 context.Context, arg1 *grpc.PayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeToken", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeToken indicates an expected call of MakeToken.
func (mr *MockMagnetiqServerMockRecorder) MakeToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeToken", reflect.TypeOf((*MockMagnetiqServer)(nil).MakeToken), arg0, arg1)
}

// OneClickPayIn mocks base method.
func (m *MockMagnetiqServer) OneClickPayIn(arg0 context.Context, arg1 *grpc.OneClickPayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OneClickPayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OneClickPayIn indicates an expected call of OneClickPayIn.
func (mr *MockMagnetiqServerMockRecorder) OneClickPayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneClickPayIn", reflect.TypeOf((*MockMagnetiqServer)(nil).OneClickPayIn), arg0, arg1)
}

// PayIn mocks base method.
func (m *MockMagnetiqServer) PayIn(arg0 context.Context, arg1 *grpc.PayInRequestData) (*grpc.PayInResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayInResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayIn indicates an expected call of PayIn.
func (mr *MockMagnetiqServerMockRecorder) PayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayIn", reflect.TypeOf((*MockMagnetiqServer)(nil).PayIn), arg0, arg1)
}

// PayOut mocks base method.
func (m *MockMagnetiqServer) PayOut(arg0 context.Context, arg1 *grpc.PayOutRequestData) (*grpc.PayOutResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOut", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayOutResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOut indicates an expected call of PayOut.
func (mr *MockMagnetiqServerMockRecorder) PayOut(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOut", reflect.TypeOf((*MockMagnetiqServer)(nil).PayOut), arg0, arg1)
}

// PayOutByPhone mocks base method.
func (m *MockMagnetiqServer) PayOutByPhone(arg0 context.Context, arg1 *grpc.PayOutByPhoneRequestData) (*grpc.PayOutResponseByPhoneData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PayOutByPhone", arg0, arg1)
	ret0, _ := ret[0].(*grpc.PayOutResponseByPhoneData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PayOutByPhone indicates an expected call of PayOutByPhone.
func (mr *MockMagnetiqServerMockRecorder) PayOutByPhone(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PayOutByPhone", reflect.TypeOf((*MockMagnetiqServer)(nil).PayOutByPhone), arg0, arg1)
}

// Refund mocks base method.
func (m *MockMagnetiqServer) Refund(arg0 context.Context, arg1 *grpc.RefundRequest) (*grpc.RefundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Refund", arg0, arg1)
	ret0, _ := ret[0].(*grpc.RefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Refund indicates an expected call of Refund.
func (mr *MockMagnetiqServerMockRecorder) Refund(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Refund", reflect.TypeOf((*MockMagnetiqServer)(nil).Refund), arg0, arg1)
}

// ResolveVisaAlias mocks base method.
func (m *MockMagnetiqServer) ResolveVisaAlias(arg0 context.Context, arg1 *grpc.ResolveVisaAliasRequest) (*grpc.ResolveVisaAliasResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResolveVisaAlias", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ResolveVisaAliasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResolveVisaAlias indicates an expected call of ResolveVisaAlias.
func (mr *MockMagnetiqServerMockRecorder) ResolveVisaAlias(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveVisaAlias", reflect.TypeOf((*MockMagnetiqServer)(nil).ResolveVisaAlias), arg0, arg1)
}

// ThreeDSConfirm mocks base method.
func (m *MockMagnetiqServer) ThreeDSConfirm(arg0 context.Context, arg1 *grpc.ThreeDSRequestData) (*grpc.ThreeDSResponseData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSConfirm", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ThreeDSResponseData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSConfirm indicates an expected call of ThreeDSConfirm.
func (mr *MockMagnetiqServerMockRecorder) ThreeDSConfirm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSConfirm", reflect.TypeOf((*MockMagnetiqServer)(nil).ThreeDSConfirm), arg0, arg1)
}

// ThreeDSResume mocks base method.
func (m *MockMagnetiqServer) ThreeDSResume(arg0 context.Context, arg1 *grpc.ThreeDSResumeRequest) (*grpc.ThreeDSResumeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ThreeDSResume", arg0, arg1)
	ret0, _ := ret[0].(*grpc.ThreeDSResumeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ThreeDSResume indicates an expected call of ThreeDSResume.
func (mr *MockMagnetiqServerMockRecorder) ThreeDSResume(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ThreeDSResume", reflect.TypeOf((*MockMagnetiqServer)(nil).ThreeDSResume), arg0, arg1)
}

// TwoStagePayIn mocks base method.
func (m *MockMagnetiqServer) TwoStagePayIn(arg0 context.Context, arg1 *grpc.TwoStagePayInRequest) (*grpc.TwoStagePayInResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TwoStagePayIn", arg0, arg1)
	ret0, _ := ret[0].(*grpc.TwoStagePayInResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TwoStagePayIn indicates an expected call of TwoStagePayIn.
func (mr *MockMagnetiqServerMockRecorder) TwoStagePayIn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TwoStagePayIn", reflect.TypeOf((*MockMagnetiqServer)(nil).TwoStagePayIn), arg0, arg1)
}

// mustEmbedUnimplementedMagnetiqServer mocks base method.
func (m *MockMagnetiqServer) mustEmbedUnimplementedMagnetiqServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMagnetiqServer")
}

// mustEmbedUnimplementedMagnetiqServer indicates an expected call of mustEmbedUnimplementedMagnetiqServer.
func (mr *MockMagnetiqServerMockRecorder) mustEmbedUnimplementedMagnetiqServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMagnetiqServer", reflect.TypeOf((*MockMagnetiqServer)(nil).mustEmbedUnimplementedMagnetiqServer))
}

// MockUnsafeMagnetiqServer is a mock of UnsafeMagnetiqServer interface.
type MockUnsafeMagnetiqServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeMagnetiqServerMockRecorder
}

// MockUnsafeMagnetiqServerMockRecorder is the mock recorder for MockUnsafeMagnetiqServer.
type MockUnsafeMagnetiqServerMockRecorder struct {
	mock *MockUnsafeMagnetiqServer
}

// NewMockUnsafeMagnetiqServer creates a new mock instance.
func NewMockUnsafeMagnetiqServer(ctrl *gomock.Controller) *MockUnsafeMagnetiqServer {
	mock := &MockUnsafeMagnetiqServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeMagnetiqServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeMagnetiqServer) EXPECT() *MockUnsafeMagnetiqServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedMagnetiqServer mocks base method.
func (m *MockUnsafeMagnetiqServer) mustEmbedUnimplementedMagnetiqServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMagnetiqServer")
}

// mustEmbedUnimplementedMagnetiqServer indicates an expected call of mustEmbedUnimplementedMagnetiqServer.
func (mr *MockUnsafeMagnetiqServerMockRecorder) mustEmbedUnimplementedMagnetiqServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMagnetiqServer", reflect.TypeOf((*MockUnsafeMagnetiqServer)(nil).mustEmbedUnimplementedMagnetiqServer))
}
