// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/card.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/descriptorpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateClientRequestV1 struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	ProjectClientId *string                `protobuf:"bytes,2,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	Email           *string                `protobuf:"bytes,3,opt,name=email" json:"email,omitempty"`
	Phone           *string                `protobuf:"bytes,4,opt,name=phone" json:"phone,omitempty"`
	Ip              *string                `protobuf:"bytes,5,opt,name=ip" json:"ip,omitempty"`
	Pan             []byte                 `protobuf:"bytes,6,opt,name=pan" json:"pan,omitempty"`
	ExpirationYear  []byte                 `protobuf:"bytes,7,opt,name=expiration_year,json=expirationYear" json:"expiration_year,omitempty"`
	ExpirationMonth []byte                 `protobuf:"bytes,8,opt,name=expiration_month,json=expirationMonth" json:"expiration_month,omitempty"`
	CardholderName  []byte                 `protobuf:"bytes,9,opt,name=cardholder_name,json=cardholderName" json:"cardholder_name,omitempty"`
	SaveAccess      *bool                  `protobuf:"varint,10,opt,name=save_access,json=saveAccess" json:"save_access,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateClientRequestV1) Reset() {
	*x = CreateClientRequestV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateClientRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateClientRequestV1) ProtoMessage() {}

func (x *CreateClientRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateClientRequestV1.ProtoReflect.Descriptor instead.
func (*CreateClientRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{0}
}

func (x *CreateClientRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CreateClientRequestV1) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

func (x *CreateClientRequestV1) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *CreateClientRequestV1) GetPhone() string {
	if x != nil && x.Phone != nil {
		return *x.Phone
	}
	return ""
}

func (x *CreateClientRequestV1) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *CreateClientRequestV1) GetPan() []byte {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *CreateClientRequestV1) GetExpirationYear() []byte {
	if x != nil {
		return x.ExpirationYear
	}
	return nil
}

func (x *CreateClientRequestV1) GetExpirationMonth() []byte {
	if x != nil {
		return x.ExpirationMonth
	}
	return nil
}

func (x *CreateClientRequestV1) GetCardholderName() []byte {
	if x != nil {
		return x.CardholderName
	}
	return nil
}

func (x *CreateClientRequestV1) GetSaveAccess() bool {
	if x != nil && x.SaveAccess != nil {
		return *x.SaveAccess
	}
	return false
}

type CreateClientResponseV1 struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	ProjectId       *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	ProjectClientId *string                `protobuf:"bytes,3,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	Ip              *string                `protobuf:"bytes,4,opt,name=ip" json:"ip,omitempty"`
	Email           *string                `protobuf:"bytes,5,opt,name=email" json:"email,omitempty"`
	Phone           *string                `protobuf:"bytes,6,opt,name=phone" json:"phone,omitempty"`
	ClientCard      *ClientCard            `protobuf:"bytes,8,opt,name=ClientCard" json:"ClientCard,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateClientResponseV1) Reset() {
	*x = CreateClientResponseV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateClientResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateClientResponseV1) ProtoMessage() {}

func (x *CreateClientResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateClientResponseV1.ProtoReflect.Descriptor instead.
func (*CreateClientResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{1}
}

func (x *CreateClientResponseV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *CreateClientResponseV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CreateClientResponseV1) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

func (x *CreateClientResponseV1) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *CreateClientResponseV1) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *CreateClientResponseV1) GetPhone() string {
	if x != nil && x.Phone != nil {
		return *x.Phone
	}
	return ""
}

func (x *CreateClientResponseV1) GetClientCard() *ClientCard {
	if x != nil {
		return x.ClientCard
	}
	return nil
}

type CreatePayOutClientRequestV1 struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	ProjectClientId *string                `protobuf:"bytes,2,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	Email           *string                `protobuf:"bytes,3,opt,name=email" json:"email,omitempty"`
	Phone           *string                `protobuf:"bytes,4,opt,name=phone" json:"phone,omitempty"`
	Ip              *string                `protobuf:"bytes,5,opt,name=ip" json:"ip,omitempty"`
	Pan             *string                `protobuf:"bytes,6,opt,name=pan" json:"pan,omitempty"`
	SaveAccess      *bool                  `protobuf:"varint,7,opt,name=save_access,json=saveAccess" json:"save_access,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreatePayOutClientRequestV1) Reset() {
	*x = CreatePayOutClientRequestV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePayOutClientRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePayOutClientRequestV1) ProtoMessage() {}

func (x *CreatePayOutClientRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePayOutClientRequestV1.ProtoReflect.Descriptor instead.
func (*CreatePayOutClientRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{2}
}

func (x *CreatePayOutClientRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CreatePayOutClientRequestV1) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

func (x *CreatePayOutClientRequestV1) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *CreatePayOutClientRequestV1) GetPhone() string {
	if x != nil && x.Phone != nil {
		return *x.Phone
	}
	return ""
}

func (x *CreatePayOutClientRequestV1) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *CreatePayOutClientRequestV1) GetPan() string {
	if x != nil && x.Pan != nil {
		return *x.Pan
	}
	return ""
}

func (x *CreatePayOutClientRequestV1) GetSaveAccess() bool {
	if x != nil && x.SaveAccess != nil {
		return *x.SaveAccess
	}
	return false
}

type CreatePayOutClientResponseV1 struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	ProjectId       *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	ProjectClientId *string                `protobuf:"bytes,3,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	Ip              *string                `protobuf:"bytes,4,opt,name=ip" json:"ip,omitempty"`
	Email           *string                `protobuf:"bytes,5,opt,name=email" json:"email,omitempty"`
	Phone           *string                `protobuf:"bytes,6,opt,name=phone" json:"phone,omitempty"`
	ClientCard      *ClientCard            `protobuf:"bytes,8,opt,name=ClientCard" json:"ClientCard,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreatePayOutClientResponseV1) Reset() {
	*x = CreatePayOutClientResponseV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePayOutClientResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePayOutClientResponseV1) ProtoMessage() {}

func (x *CreatePayOutClientResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePayOutClientResponseV1.ProtoReflect.Descriptor instead.
func (*CreatePayOutClientResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{3}
}

func (x *CreatePayOutClientResponseV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *CreatePayOutClientResponseV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CreatePayOutClientResponseV1) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

func (x *CreatePayOutClientResponseV1) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *CreatePayOutClientResponseV1) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *CreatePayOutClientResponseV1) GetPhone() string {
	if x != nil && x.Phone != nil {
		return *x.Phone
	}
	return ""
}

func (x *CreatePayOutClientResponseV1) GetClientCard() *ClientCard {
	if x != nil {
		return x.ClientCard
	}
	return nil
}

type ClientCard struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	IpsId         *uint64                `protobuf:"varint,2,opt,name=ips_id,json=ipsId" json:"ips_id,omitempty"`
	MaskedPan     *string                `protobuf:"bytes,3,opt,name=masked_pan,json=maskedPan" json:"masked_pan,omitempty"`
	CountryId     *uint64                `protobuf:"varint,4,opt,name=country_id,json=countryId" json:"country_id,omitempty"`
	IssuerId      *uint64                `protobuf:"varint,5,opt,name=issuer_id,json=issuerId" json:"issuer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientCard) Reset() {
	*x = ClientCard{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientCard) ProtoMessage() {}

func (x *ClientCard) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientCard.ProtoReflect.Descriptor instead.
func (*ClientCard) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{4}
}

func (x *ClientCard) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ClientCard) GetIpsId() uint64 {
	if x != nil && x.IpsId != nil {
		return *x.IpsId
	}
	return 0
}

func (x *ClientCard) GetMaskedPan() string {
	if x != nil && x.MaskedPan != nil {
		return *x.MaskedPan
	}
	return ""
}

func (x *ClientCard) GetCountryId() uint64 {
	if x != nil && x.CountryId != nil {
		return *x.CountryId
	}
	return 0
}

func (x *ClientCard) GetIssuerId() uint64 {
	if x != nil && x.IssuerId != nil {
		return *x.IssuerId
	}
	return 0
}

type GetCardTokensRequestV1 struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	ProjectClientId *string                `protobuf:"bytes,2,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	CardId          *uint64                `protobuf:"varint,3,opt,name=card_id,json=cardId" json:"card_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetCardTokensRequestV1) Reset() {
	*x = GetCardTokensRequestV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCardTokensRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardTokensRequestV1) ProtoMessage() {}

func (x *GetCardTokensRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardTokensRequestV1.ProtoReflect.Descriptor instead.
func (*GetCardTokensRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{5}
}

func (x *GetCardTokensRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetCardTokensRequestV1) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

func (x *GetCardTokensRequestV1) GetCardId() uint64 {
	if x != nil && x.CardId != nil {
		return *x.CardId
	}
	return 0
}

type GetCardTokensResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Card          *ClientCard            `protobuf:"bytes,1,opt,name=card" json:"card,omitempty"`
	Tokens        []*CardTokenV1         `protobuf:"bytes,2,rep,name=tokens" json:"tokens,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCardTokensResponseV1) Reset() {
	*x = GetCardTokensResponseV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCardTokensResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardTokensResponseV1) ProtoMessage() {}

func (x *GetCardTokensResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardTokensResponseV1.ProtoReflect.Descriptor instead.
func (*GetCardTokensResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{6}
}

func (x *GetCardTokensResponseV1) GetCard() *ClientCard {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *GetCardTokensResponseV1) GetTokens() []*CardTokenV1 {
	if x != nil {
		return x.Tokens
	}
	return nil
}

type CardTokenV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	AcquirerId    *uint64                `protobuf:"varint,2,opt,name=acquirer_id,json=acquirerId" json:"acquirer_id,omitempty"`
	TerminalId    *uint64                `protobuf:"varint,3,opt,name=terminal_id,json=terminalId" json:"terminal_id,omitempty"`
	CardId        *uint64                `protobuf:"varint,4,opt,name=card_id,json=cardId" json:"card_id,omitempty"`
	Token         *string                `protobuf:"bytes,5,opt,name=token" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CardTokenV1) Reset() {
	*x = CardTokenV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CardTokenV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardTokenV1) ProtoMessage() {}

func (x *CardTokenV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardTokenV1.ProtoReflect.Descriptor instead.
func (*CardTokenV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{7}
}

func (x *CardTokenV1) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *CardTokenV1) GetAcquirerId() uint64 {
	if x != nil && x.AcquirerId != nil {
		return *x.AcquirerId
	}
	return 0
}

func (x *CardTokenV1) GetTerminalId() uint64 {
	if x != nil && x.TerminalId != nil {
		return *x.TerminalId
	}
	return 0
}

func (x *CardTokenV1) GetCardId() uint64 {
	if x != nil && x.CardId != nil {
		return *x.CardId
	}
	return 0
}

func (x *CardTokenV1) GetToken() string {
	if x != nil && x.Token != nil {
		return *x.Token
	}
	return ""
}

type GetOneClickPayInCardsRequestV1 struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ProjectId         *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	ProjectClientId   *string                `protobuf:"bytes,2,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	TransactionTypeId *uint64                `protobuf:"varint,3,opt,name=transaction_type_id,json=transactionTypeId" json:"transaction_type_id,omitempty"`
	Amount            *float64               `protobuf:"fixed64,4,opt,name=amount" json:"amount,omitempty"`
	ActiveTerminals   []*TerminalProjectV1   `protobuf:"bytes,5,rep,name=activeTerminals" json:"activeTerminals,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetOneClickPayInCardsRequestV1) Reset() {
	*x = GetOneClickPayInCardsRequestV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOneClickPayInCardsRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOneClickPayInCardsRequestV1) ProtoMessage() {}

func (x *GetOneClickPayInCardsRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOneClickPayInCardsRequestV1.ProtoReflect.Descriptor instead.
func (*GetOneClickPayInCardsRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{8}
}

func (x *GetOneClickPayInCardsRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetOneClickPayInCardsRequestV1) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

func (x *GetOneClickPayInCardsRequestV1) GetTransactionTypeId() uint64 {
	if x != nil && x.TransactionTypeId != nil {
		return *x.TransactionTypeId
	}
	return 0
}

func (x *GetOneClickPayInCardsRequestV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *GetOneClickPayInCardsRequestV1) GetActiveTerminals() []*TerminalProjectV1 {
	if x != nil {
		return x.ActiveTerminals
	}
	return nil
}

type GetOneClickPayInCardsResponseV1 struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Cards         []*GetEncryptedCardResponseV1 `protobuf:"bytes,1,rep,name=cards" json:"cards,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOneClickPayInCardsResponseV1) Reset() {
	*x = GetOneClickPayInCardsResponseV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOneClickPayInCardsResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOneClickPayInCardsResponseV1) ProtoMessage() {}

func (x *GetOneClickPayInCardsResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOneClickPayInCardsResponseV1.ProtoReflect.Descriptor instead.
func (*GetOneClickPayInCardsResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{9}
}

func (x *GetOneClickPayInCardsResponseV1) GetCards() []*GetEncryptedCardResponseV1 {
	if x != nil {
		return x.Cards
	}
	return nil
}

type GetOneClickPayOutCardsRequestV1 struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	ProjectClientId *string                `protobuf:"bytes,2,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	Amount          *float64               `protobuf:"fixed64,3,opt,name=amount" json:"amount,omitempty"`
	ActiveTerminals []*TerminalProjectV1   `protobuf:"bytes,4,rep,name=activeTerminals" json:"activeTerminals,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetOneClickPayOutCardsRequestV1) Reset() {
	*x = GetOneClickPayOutCardsRequestV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOneClickPayOutCardsRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOneClickPayOutCardsRequestV1) ProtoMessage() {}

func (x *GetOneClickPayOutCardsRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOneClickPayOutCardsRequestV1.ProtoReflect.Descriptor instead.
func (*GetOneClickPayOutCardsRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{10}
}

func (x *GetOneClickPayOutCardsRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetOneClickPayOutCardsRequestV1) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

func (x *GetOneClickPayOutCardsRequestV1) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *GetOneClickPayOutCardsRequestV1) GetActiveTerminals() []*TerminalProjectV1 {
	if x != nil {
		return x.ActiveTerminals
	}
	return nil
}

type GetOneClickPayOutCardsResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cards         map[string]string      `protobuf:"bytes,1,rep,name=cards" json:"cards,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOneClickPayOutCardsResponseV1) Reset() {
	*x = GetOneClickPayOutCardsResponseV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOneClickPayOutCardsResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOneClickPayOutCardsResponseV1) ProtoMessage() {}

func (x *GetOneClickPayOutCardsResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOneClickPayOutCardsResponseV1.ProtoReflect.Descriptor instead.
func (*GetOneClickPayOutCardsResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{11}
}

func (x *GetOneClickPayOutCardsResponseV1) GetCards() map[string]string {
	if x != nil {
		return x.Cards
	}
	return nil
}

type GetEncryptedCardRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CardId        *uint64                `protobuf:"varint,1,opt,name=card_id,json=cardId" json:"card_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEncryptedCardRequestV1) Reset() {
	*x = GetEncryptedCardRequestV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEncryptedCardRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEncryptedCardRequestV1) ProtoMessage() {}

func (x *GetEncryptedCardRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEncryptedCardRequestV1.ProtoReflect.Descriptor instead.
func (*GetEncryptedCardRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{12}
}

func (x *GetEncryptedCardRequestV1) GetCardId() uint64 {
	if x != nil && x.CardId != nil {
		return *x.CardId
	}
	return 0
}

type GetEncryptedCardResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CardToken     *string                `protobuf:"bytes,1,opt,name=card_token,json=cardToken" json:"card_token,omitempty"`
	MaskedPan     *string                `protobuf:"bytes,2,opt,name=masked_pan,json=maskedPan" json:"masked_pan,omitempty"`
	Ips           *string                `protobuf:"bytes,3,opt,name=ips" json:"ips,omitempty"`
	Issuer        *string                `protobuf:"bytes,4,opt,name=issuer" json:"issuer,omitempty"`
	Year          *string                `protobuf:"bytes,5,opt,name=year" json:"year,omitempty"`
	Month         *string                `protobuf:"bytes,6,opt,name=month" json:"month,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEncryptedCardResponseV1) Reset() {
	*x = GetEncryptedCardResponseV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEncryptedCardResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEncryptedCardResponseV1) ProtoMessage() {}

func (x *GetEncryptedCardResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEncryptedCardResponseV1.ProtoReflect.Descriptor instead.
func (*GetEncryptedCardResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{13}
}

func (x *GetEncryptedCardResponseV1) GetCardToken() string {
	if x != nil && x.CardToken != nil {
		return *x.CardToken
	}
	return ""
}

func (x *GetEncryptedCardResponseV1) GetMaskedPan() string {
	if x != nil && x.MaskedPan != nil {
		return *x.MaskedPan
	}
	return ""
}

func (x *GetEncryptedCardResponseV1) GetIps() string {
	if x != nil && x.Ips != nil {
		return *x.Ips
	}
	return ""
}

func (x *GetEncryptedCardResponseV1) GetIssuer() string {
	if x != nil && x.Issuer != nil {
		return *x.Issuer
	}
	return ""
}

func (x *GetEncryptedCardResponseV1) GetYear() string {
	if x != nil && x.Year != nil {
		return *x.Year
	}
	return ""
}

func (x *GetEncryptedCardResponseV1) GetMonth() string {
	if x != nil && x.Month != nil {
		return *x.Month
	}
	return ""
}

type GetEncryptedPayOutCardResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CardToken     *string                `protobuf:"bytes,1,opt,name=card_token,json=cardToken" json:"card_token,omitempty"`
	MaskedPan     *string                `protobuf:"bytes,2,opt,name=masked_pan,json=maskedPan" json:"masked_pan,omitempty"`
	Ips           *string                `protobuf:"bytes,3,opt,name=ips" json:"ips,omitempty"`
	Issuer        *string                `protobuf:"bytes,4,opt,name=issuer" json:"issuer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEncryptedPayOutCardResponseV1) Reset() {
	*x = GetEncryptedPayOutCardResponseV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEncryptedPayOutCardResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEncryptedPayOutCardResponseV1) ProtoMessage() {}

func (x *GetEncryptedPayOutCardResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEncryptedPayOutCardResponseV1.ProtoReflect.Descriptor instead.
func (*GetEncryptedPayOutCardResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{14}
}

func (x *GetEncryptedPayOutCardResponseV1) GetCardToken() string {
	if x != nil && x.CardToken != nil {
		return *x.CardToken
	}
	return ""
}

func (x *GetEncryptedPayOutCardResponseV1) GetMaskedPan() string {
	if x != nil && x.MaskedPan != nil {
		return *x.MaskedPan
	}
	return ""
}

func (x *GetEncryptedPayOutCardResponseV1) GetIps() string {
	if x != nil && x.Ips != nil {
		return *x.Ips
	}
	return ""
}

func (x *GetEncryptedPayOutCardResponseV1) GetIssuer() string {
	if x != nil && x.Issuer != nil {
		return *x.Issuer
	}
	return ""
}

type GetPanByCardIdRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CardId        *uint64                `protobuf:"varint,1,opt,name=card_id,json=cardId" json:"card_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPanByCardIdRequestV1) Reset() {
	*x = GetPanByCardIdRequestV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPanByCardIdRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPanByCardIdRequestV1) ProtoMessage() {}

func (x *GetPanByCardIdRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPanByCardIdRequestV1.ProtoReflect.Descriptor instead.
func (*GetPanByCardIdRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{15}
}

func (x *GetPanByCardIdRequestV1) GetCardId() uint64 {
	if x != nil && x.CardId != nil {
		return *x.CardId
	}
	return 0
}

type GetPanByHashedIdRequestV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EncryptedKey  *string                `protobuf:"bytes,1,opt,name=encrypted_key,json=encryptedKey" json:"encrypted_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPanByHashedIdRequestV1) Reset() {
	*x = GetPanByHashedIdRequestV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPanByHashedIdRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPanByHashedIdRequestV1) ProtoMessage() {}

func (x *GetPanByHashedIdRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPanByHashedIdRequestV1.ProtoReflect.Descriptor instead.
func (*GetPanByHashedIdRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{16}
}

func (x *GetPanByHashedIdRequestV1) GetEncryptedKey() string {
	if x != nil && x.EncryptedKey != nil {
		return *x.EncryptedKey
	}
	return ""
}

type GetPanResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MaskedPan     *string                `protobuf:"bytes,1,opt,name=masked_pan,json=maskedPan" json:"masked_pan,omitempty"`
	IpsId         *uint64                `protobuf:"varint,2,opt,name=ips_id,json=ipsId" json:"ips_id,omitempty"`
	CountryId     *uint64                `protobuf:"varint,3,opt,name=country_id,json=countryId" json:"country_id,omitempty"`
	IssuerId      *uint64                `protobuf:"varint,4,opt,name=issuer_id,json=issuerId" json:"issuer_id,omitempty"`
	CardId        *uint64                `protobuf:"varint,5,opt,name=card_id,json=cardId" json:"card_id,omitempty"`
	Pan           []byte                 `protobuf:"bytes,6,opt,name=pan" json:"pan,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPanResponseV1) Reset() {
	*x = GetPanResponseV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPanResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPanResponseV1) ProtoMessage() {}

func (x *GetPanResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPanResponseV1.ProtoReflect.Descriptor instead.
func (*GetPanResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{17}
}

func (x *GetPanResponseV1) GetMaskedPan() string {
	if x != nil && x.MaskedPan != nil {
		return *x.MaskedPan
	}
	return ""
}

func (x *GetPanResponseV1) GetIpsId() uint64 {
	if x != nil && x.IpsId != nil {
		return *x.IpsId
	}
	return 0
}

func (x *GetPanResponseV1) GetCountryId() uint64 {
	if x != nil && x.CountryId != nil {
		return *x.CountryId
	}
	return 0
}

func (x *GetPanResponseV1) GetIssuerId() uint64 {
	if x != nil && x.IssuerId != nil {
		return *x.IssuerId
	}
	return 0
}

func (x *GetPanResponseV1) GetCardId() uint64 {
	if x != nil && x.CardId != nil {
		return *x.CardId
	}
	return 0
}

func (x *GetPanResponseV1) GetPan() []byte {
	if x != nil {
		return x.Pan
	}
	return nil
}

type GetClientListByVerificationRequestV1 struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	ClientCreatedAtFrom *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=client_created_at_from,json=clientCreatedAtFrom" json:"client_created_at_from,omitempty"`
	ClientCreatedAtTo   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=client_created_at_to,json=clientCreatedAtTo" json:"client_created_at_to,omitempty"`
	VerificationUserId  *uint64                `protobuf:"varint,3,opt,name=verification_user_id,json=verificationUserId" json:"verification_user_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *GetClientListByVerificationRequestV1) Reset() {
	*x = GetClientListByVerificationRequestV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientListByVerificationRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientListByVerificationRequestV1) ProtoMessage() {}

func (x *GetClientListByVerificationRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientListByVerificationRequestV1.ProtoReflect.Descriptor instead.
func (*GetClientListByVerificationRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{18}
}

func (x *GetClientListByVerificationRequestV1) GetClientCreatedAtFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.ClientCreatedAtFrom
	}
	return nil
}

func (x *GetClientListByVerificationRequestV1) GetClientCreatedAtTo() *timestamppb.Timestamp {
	if x != nil {
		return x.ClientCreatedAtTo
	}
	return nil
}

func (x *GetClientListByVerificationRequestV1) GetVerificationUserId() uint64 {
	if x != nil && x.VerificationUserId != nil {
		return *x.VerificationUserId
	}
	return 0
}

type GetClientListByProjectClientRequestV1 struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Data          []*ProjectAndClientData `protobuf:"bytes,1,rep,name=data" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientListByProjectClientRequestV1) Reset() {
	*x = GetClientListByProjectClientRequestV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientListByProjectClientRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientListByProjectClientRequestV1) ProtoMessage() {}

func (x *GetClientListByProjectClientRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientListByProjectClientRequestV1.ProtoReflect.Descriptor instead.
func (*GetClientListByProjectClientRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{19}
}

func (x *GetClientListByProjectClientRequestV1) GetData() []*ProjectAndClientData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ProjectAndClientData struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	ProjectClientId *string                `protobuf:"bytes,2,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ProjectAndClientData) Reset() {
	*x = ProjectAndClientData{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProjectAndClientData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectAndClientData) ProtoMessage() {}

func (x *ProjectAndClientData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectAndClientData.ProtoReflect.Descriptor instead.
func (*ProjectAndClientData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{20}
}

func (x *ProjectAndClientData) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *ProjectAndClientData) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

type GetClientListByProjectClientResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*GetClientListData   `protobuf:"bytes,1,rep,name=data" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientListByProjectClientResponseV1) Reset() {
	*x = GetClientListByProjectClientResponseV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientListByProjectClientResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientListByProjectClientResponseV1) ProtoMessage() {}

func (x *GetClientListByProjectClientResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientListByProjectClientResponseV1.ProtoReflect.Descriptor instead.
func (*GetClientListByProjectClientResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{21}
}

func (x *GetClientListByProjectClientResponseV1) GetData() []*GetClientListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetClientListData struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ClientId           *uint64                `protobuf:"varint,1,opt,name=client_id,json=clientId" json:"client_id,omitempty"`
	ProjectId          *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	ProjectClientId    *string                `protobuf:"bytes,3,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	VerificationUserId *uint64                `protobuf:"varint,4,opt,name=verification_user_id,json=verificationUserId" json:"verification_user_id,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetClientListData) Reset() {
	*x = GetClientListData{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientListData) ProtoMessage() {}

func (x *GetClientListData) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientListData.ProtoReflect.Descriptor instead.
func (*GetClientListData) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{22}
}

func (x *GetClientListData) GetClientId() uint64 {
	if x != nil && x.ClientId != nil {
		return *x.ClientId
	}
	return 0
}

func (x *GetClientListData) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetClientListData) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

func (x *GetClientListData) GetVerificationUserId() uint64 {
	if x != nil && x.VerificationUserId != nil {
		return *x.VerificationUserId
	}
	return 0
}

type CheckClientActivenessRequestV1 struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	ProjectClientId *string                `protobuf:"bytes,2,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CheckClientActivenessRequestV1) Reset() {
	*x = CheckClientActivenessRequestV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckClientActivenessRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckClientActivenessRequestV1) ProtoMessage() {}

func (x *CheckClientActivenessRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckClientActivenessRequestV1.ProtoReflect.Descriptor instead.
func (*CheckClientActivenessRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{23}
}

func (x *CheckClientActivenessRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *CheckClientActivenessRequestV1) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

type GetCardByPanRequestV1 struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       *uint64                `protobuf:"varint,1,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	ProjectClientId *string                `protobuf:"bytes,2,opt,name=project_client_id,json=projectClientId" json:"project_client_id,omitempty"`
	Pan             []byte                 `protobuf:"bytes,3,opt,name=pan" json:"pan,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetCardByPanRequestV1) Reset() {
	*x = GetCardByPanRequestV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCardByPanRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardByPanRequestV1) ProtoMessage() {}

func (x *GetCardByPanRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardByPanRequestV1.ProtoReflect.Descriptor instead.
func (*GetCardByPanRequestV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{24}
}

func (x *GetCardByPanRequestV1) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

func (x *GetCardByPanRequestV1) GetProjectClientId() string {
	if x != nil && x.ProjectClientId != nil {
		return *x.ProjectClientId
	}
	return ""
}

func (x *GetCardByPanRequestV1) GetPan() []byte {
	if x != nil {
		return x.Pan
	}
	return nil
}

type GetCardByPanResponseV1 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CardId        *uint64                `protobuf:"varint,1,opt,name=card_id,json=cardId" json:"card_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCardByPanResponseV1) Reset() {
	*x = GetCardByPanResponseV1{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCardByPanResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardByPanResponseV1) ProtoMessage() {}

func (x *GetCardByPanResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardByPanResponseV1.ProtoReflect.Descriptor instead.
func (*GetCardByPanResponseV1) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{25}
}

func (x *GetCardByPanResponseV1) GetCardId() uint64 {
	if x != nil && x.CardId != nil {
		return *x.CardId
	}
	return 0
}

type DecryptPayInRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EncryptedCard []byte                 `protobuf:"bytes,1,opt,name=encrypted_card,json=encryptedCard" json:"encrypted_card,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DecryptPayInRequest) Reset() {
	*x = DecryptPayInRequest{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecryptPayInRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecryptPayInRequest) ProtoMessage() {}

func (x *DecryptPayInRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecryptPayInRequest.ProtoReflect.Descriptor instead.
func (*DecryptPayInRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{26}
}

func (x *DecryptPayInRequest) GetEncryptedCard() []byte {
	if x != nil {
		return x.EncryptedCard
	}
	return nil
}

type DecryptPayOutRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EncryptedCard []byte                 `protobuf:"bytes,1,opt,name=encrypted_card,json=encryptedCard" json:"encrypted_card,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DecryptPayOutRequest) Reset() {
	*x = DecryptPayOutRequest{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecryptPayOutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecryptPayOutRequest) ProtoMessage() {}

func (x *DecryptPayOutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecryptPayOutRequest.ProtoReflect.Descriptor instead.
func (*DecryptPayOutRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{27}
}

func (x *DecryptPayOutRequest) GetEncryptedCard() []byte {
	if x != nil {
		return x.EncryptedCard
	}
	return nil
}

type ReEncryptCardRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pan           []byte                 `protobuf:"bytes,1,opt,name=pan" json:"pan,omitempty"`
	ExpMonth      []byte                 `protobuf:"bytes,2,opt,name=exp_month,json=expMonth" json:"exp_month,omitempty"`
	ExpYear       []byte                 `protobuf:"bytes,3,opt,name=exp_year,json=expYear" json:"exp_year,omitempty"`
	Cvc           []byte                 `protobuf:"bytes,4,opt,name=cvc" json:"cvc,omitempty"`
	FullName      []byte                 `protobuf:"bytes,5,opt,name=full_name,json=fullName" json:"full_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReEncryptCardRequest) Reset() {
	*x = ReEncryptCardRequest{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReEncryptCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReEncryptCardRequest) ProtoMessage() {}

func (x *ReEncryptCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReEncryptCardRequest.ProtoReflect.Descriptor instead.
func (*ReEncryptCardRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{28}
}

func (x *ReEncryptCardRequest) GetPan() []byte {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *ReEncryptCardRequest) GetExpMonth() []byte {
	if x != nil {
		return x.ExpMonth
	}
	return nil
}

func (x *ReEncryptCardRequest) GetExpYear() []byte {
	if x != nil {
		return x.ExpYear
	}
	return nil
}

func (x *ReEncryptCardRequest) GetCvc() []byte {
	if x != nil {
		return x.Cvc
	}
	return nil
}

func (x *ReEncryptCardRequest) GetFullName() []byte {
	if x != nil {
		return x.FullName
	}
	return nil
}

type DecryptPayInResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pan           []byte                 `protobuf:"bytes,1,opt,name=pan" json:"pan,omitempty"`
	ExpMonth      []byte                 `protobuf:"bytes,2,opt,name=exp_month,json=expMonth" json:"exp_month,omitempty"`
	ExpYear       []byte                 `protobuf:"bytes,3,opt,name=exp_year,json=expYear" json:"exp_year,omitempty"`
	Cvc           []byte                 `protobuf:"bytes,4,opt,name=cvc" json:"cvc,omitempty"`
	FullName      []byte                 `protobuf:"bytes,5,opt,name=full_name,json=fullName" json:"full_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DecryptPayInResponse) Reset() {
	*x = DecryptPayInResponse{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecryptPayInResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecryptPayInResponse) ProtoMessage() {}

func (x *DecryptPayInResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecryptPayInResponse.ProtoReflect.Descriptor instead.
func (*DecryptPayInResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{29}
}

func (x *DecryptPayInResponse) GetPan() []byte {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *DecryptPayInResponse) GetExpMonth() []byte {
	if x != nil {
		return x.ExpMonth
	}
	return nil
}

func (x *DecryptPayInResponse) GetExpYear() []byte {
	if x != nil {
		return x.ExpYear
	}
	return nil
}

func (x *DecryptPayInResponse) GetCvc() []byte {
	if x != nil {
		return x.Cvc
	}
	return nil
}

func (x *DecryptPayInResponse) GetFullName() []byte {
	if x != nil {
		return x.FullName
	}
	return nil
}

type DecryptPayOutResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pan           []byte                 `protobuf:"bytes,1,opt,name=pan" json:"pan,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DecryptPayOutResponse) Reset() {
	*x = DecryptPayOutResponse{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DecryptPayOutResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecryptPayOutResponse) ProtoMessage() {}

func (x *DecryptPayOutResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecryptPayOutResponse.ProtoReflect.Descriptor instead.
func (*DecryptPayOutResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{30}
}

func (x *DecryptPayOutResponse) GetPan() []byte {
	if x != nil {
		return x.Pan
	}
	return nil
}

type ReEncryptCardResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pan           []byte                 `protobuf:"bytes,1,opt,name=pan" json:"pan,omitempty"`
	ExpMonth      []byte                 `protobuf:"bytes,2,opt,name=exp_month,json=expMonth" json:"exp_month,omitempty"`
	ExpYear       []byte                 `protobuf:"bytes,3,opt,name=exp_year,json=expYear" json:"exp_year,omitempty"`
	Cvc           []byte                 `protobuf:"bytes,4,opt,name=cvc" json:"cvc,omitempty"`
	FullName      []byte                 `protobuf:"bytes,5,opt,name=full_name,json=fullName" json:"full_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReEncryptCardResponse) Reset() {
	*x = ReEncryptCardResponse{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReEncryptCardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReEncryptCardResponse) ProtoMessage() {}

func (x *ReEncryptCardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReEncryptCardResponse.ProtoReflect.Descriptor instead.
func (*ReEncryptCardResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{31}
}

func (x *ReEncryptCardResponse) GetPan() []byte {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *ReEncryptCardResponse) GetExpMonth() []byte {
	if x != nil {
		return x.ExpMonth
	}
	return nil
}

func (x *ReEncryptCardResponse) GetExpYear() []byte {
	if x != nil {
		return x.ExpYear
	}
	return nil
}

func (x *ReEncryptCardResponse) GetCvc() []byte {
	if x != nil {
		return x.Cvc
	}
	return nil
}

func (x *ReEncryptCardResponse) GetFullName() []byte {
	if x != nil {
		return x.FullName
	}
	return nil
}

type GetPanInfoByProjectIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pan           []byte                 `protobuf:"bytes,1,opt,name=pan" json:"pan,omitempty"`
	ProjectId     *uint64                `protobuf:"varint,2,opt,name=project_id,json=projectId" json:"project_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPanInfoByProjectIdRequest) Reset() {
	*x = GetPanInfoByProjectIdRequest{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPanInfoByProjectIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPanInfoByProjectIdRequest) ProtoMessage() {}

func (x *GetPanInfoByProjectIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPanInfoByProjectIdRequest.ProtoReflect.Descriptor instead.
func (*GetPanInfoByProjectIdRequest) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{32}
}

func (x *GetPanInfoByProjectIdRequest) GetPan() []byte {
	if x != nil {
		return x.Pan
	}
	return nil
}

func (x *GetPanInfoByProjectIdRequest) GetProjectId() uint64 {
	if x != nil && x.ProjectId != nil {
		return *x.ProjectId
	}
	return 0
}

type GetPanInfoByProjectIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IpsId         *uint64                `protobuf:"varint,1,opt,name=ips_id,json=ipsId" json:"ips_id,omitempty"`
	CountryId     *uint64                `protobuf:"varint,2,opt,name=country_id,json=countryId" json:"country_id,omitempty"`
	IssuerId      *uint64                `protobuf:"varint,3,opt,name=issuer_id,json=issuerId" json:"issuer_id,omitempty"`
	MaskedPan     *string                `protobuf:"bytes,4,opt,name=masked_pan,json=maskedPan" json:"masked_pan,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPanInfoByProjectIdResponse) Reset() {
	*x = GetPanInfoByProjectIdResponse{}
	mi := &file_inner_processing_grpc_card_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPanInfoByProjectIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPanInfoByProjectIdResponse) ProtoMessage() {}

func (x *GetPanInfoByProjectIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_card_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPanInfoByProjectIdResponse.ProtoReflect.Descriptor instead.
func (*GetPanInfoByProjectIdResponse) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_card_proto_rawDescGZIP(), []int{33}
}

func (x *GetPanInfoByProjectIdResponse) GetIpsId() uint64 {
	if x != nil && x.IpsId != nil {
		return *x.IpsId
	}
	return 0
}

func (x *GetPanInfoByProjectIdResponse) GetCountryId() uint64 {
	if x != nil && x.CountryId != nil {
		return *x.CountryId
	}
	return 0
}

func (x *GetPanInfoByProjectIdResponse) GetIssuerId() uint64 {
	if x != nil && x.IssuerId != nil {
		return *x.IssuerId
	}
	return 0
}

func (x *GetPanInfoByProjectIdResponse) GetMaskedPan() string {
	if x != nil && x.MaskedPan != nil {
		return *x.MaskedPan
	}
	return ""
}

var File_inner_processing_grpc_card_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_card_proto_rawDesc = string([]byte{
	0x0a, 0x20, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x14, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x69, 0x6e, 0x6e, 0x65, 0x72,
	0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63,
	0x2f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x16, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe0, 0x02, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x1d, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70,
	0x12, 0x19, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0x9a,
	0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x59, 0x65, 0x61, 0x72, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12,
	0x27, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x63, 0x61, 0x72, 0x64, 0x68, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x61, 0x76, 0x65,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x73,
	0x61, 0x76, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xfa, 0x01, 0x0a, 0x16, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1d, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x05, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x12, 0x40, 0x0a, 0x0a, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x0a, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x22, 0xe9, 0x01, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1d, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x19, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x70, 0x61,
	0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x61, 0x76, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x73, 0x61, 0x76, 0x65, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x22, 0x80, 0x02, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79,
	0x4f, 0x75, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1d, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x05, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x12, 0x40, 0x0a, 0x0a, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x0a, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x22, 0x8e, 0x01, 0x0a, 0x0a, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x70, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x69, 0x70, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d,
	0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x70, 0x61, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x50, 0x61, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73,
	0x75, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x69, 0x73,
	0x73, 0x75, 0x65, 0x72, 0x49, 0x64, 0x22, 0x7c, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x61,
	0x72, 0x64, 0x49, 0x64, 0x22, 0x93, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31,
	0x12, 0x34, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12, 0x42, 0x0a, 0x06, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x56, 0x31, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02,
	0x01, 0x64, 0x52, 0x06, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x0b, 0x43,
	0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0a, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63,
	0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x8e, 0x02, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x43,
	0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x59, 0x0a, 0x0f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x56, 0x31, 0x52, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x73, 0x22, 0x69, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x43,
	0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x46, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73,
	0x22, 0xdf, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b,
	0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x59, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x2e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x2e,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x56,
	0x31, 0x52, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x73, 0x22, 0xb5, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69,
	0x63, 0x6b, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x57, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x47, 0x65,
	0x74, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x2e, 0x43,
	0x61, 0x72, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73,
	0x1a, 0x38, 0x0a, 0x0a, 0x43, 0x61, 0x72, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x34, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x22, 0xb7, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65,
	0x64, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12,
	0x26, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x09, 0x63, 0x61,
	0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x73, 0x6b, 0x65,
	0x64, 0x5f, 0x70, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x73,
	0x6b, 0x65, 0x64, 0x50, 0x61, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x70, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x70, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x73, 0x75,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x79, 0x65, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x22, 0x93, 0x01, 0x0a, 0x20, 0x47,
	0x65, 0x74, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x50, 0x61, 0x79, 0x4f, 0x75,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12,
	0x26, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x09, 0x63, 0x61,
	0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x73, 0x6b, 0x65,
	0x64, 0x5f, 0x70, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x73,
	0x6b, 0x65, 0x64, 0x50, 0x61, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x70, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x70, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x73, 0x75,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72,
	0x22, 0x32, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x42, 0x79, 0x43, 0x61, 0x72, 0x64,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x61,
	0x72, 0x64, 0x49, 0x64, 0x22, 0x49, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x42, 0x79,
	0x48, 0x61, 0x73, 0x68, 0x65, 0x64, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01,
	0x64, 0x52, 0x0c, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x4b, 0x65, 0x79, 0x22,
	0xb8, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x70,
	0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64,
	0x50, 0x61, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x70, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x05, 0x69, 0x70, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73,
	0x75, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x69, 0x73,
	0x73, 0x75, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0x9a, 0xfc,
	0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x22, 0xf6, 0x01, 0x0a, 0x24, 0x47,
	0x65, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x12, 0x4f, 0x0a, 0x16, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x13, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x46, 0x72, 0x6f, 0x6d, 0x12, 0x4b, 0x0a, 0x14, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x11,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x54,
	0x6f, 0x12, 0x30, 0x0a, 0x14, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x12, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x22, 0x67, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x3e, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x41, 0x6e, 0x64, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x61, 0x0a, 0x14,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x41, 0x6e, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0x65, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xad, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x12, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x6b, 0x0a, 0x1e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x22, 0x7d, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x79,
	0x50, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x70,
	0x61, 0x6e, 0x22, 0x31, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x79, 0x50,
	0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x17, 0x0a, 0x07,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63,
	0x61, 0x72, 0x64, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x13, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x43,
	0x61, 0x72, 0x64, 0x22, 0x3d, 0x0a, 0x14, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x50, 0x61,
	0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x65,
	0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0d, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x43, 0x61,
	0x72, 0x64, 0x22, 0xa1, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x03, 0x70,
	0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01,
	0x64, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x5f, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x65, 0x78, 0x70, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x65, 0x78, 0x70, 0x59, 0x65, 0x61, 0x72, 0x12, 0x19,
	0x0a, 0x03, 0x63, 0x76, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0x9a, 0xfc, 0xeb,
	0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x63, 0x76, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x75, 0x6c,
	0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x75,
	0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa1, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x63, 0x72, 0x79,
	0x70, 0x74, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x19, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0x9a, 0xfc,
	0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78,
	0x70, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x65,
	0x78, 0x70, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x5f, 0x79,
	0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x65, 0x78, 0x70, 0x59, 0x65,
	0x61, 0x72, 0x12, 0x19, 0x0a, 0x03, 0x63, 0x76, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x42,
	0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x63, 0x76, 0x63, 0x12, 0x1b, 0x0a,
	0x09, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x32, 0x0a, 0x15, 0x44, 0x65,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x22, 0xa2,
	0x01, 0x0a, 0x15, 0x52, 0x65, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03,
	0x70, 0x61, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x65, 0x78, 0x70, 0x4d, 0x6f, 0x6e, 0x74, 0x68,
	0x12, 0x19, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x07, 0x65, 0x78, 0x70, 0x59, 0x65, 0x61, 0x72, 0x12, 0x19, 0x0a, 0x03, 0x63,
	0x76, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01,
	0x64, 0x52, 0x03, 0x63, 0x76, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x58, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x42, 0x07, 0x9a, 0xfc, 0xeb, 0x8e, 0x02, 0x01, 0x64, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x91, 0x01,
	0x0a, 0x1d, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x15, 0x0a, 0x06, 0x69, 0x70, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x05, 0x69, 0x70, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x70, 0x61, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x50, 0x61,
	0x6e, 0x32, 0x80, 0x11, 0x0a, 0x04, 0x43, 0x61, 0x72, 0x64, 0x12, 0x6d, 0x0a, 0x0e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x31, 0x12, 0x2b, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x2c, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x56, 0x31, 0x12, 0x2c, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x2d, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x88, 0x01, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x49, 0x6e,
	0x43, 0x61, 0x72, 0x64, 0x73, 0x56, 0x31, 0x12, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x47,
	0x65, 0x74, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x43,
	0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x35, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b,
	0x50, 0x61, 0x79, 0x49, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e,
	0x42, 0x79, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x56, 0x31, 0x12, 0x2d, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x42, 0x79, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x26, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56,
	0x31, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x42, 0x79, 0x48,
	0x61, 0x73, 0x68, 0x65, 0x64, 0x49, 0x64, 0x56, 0x31, 0x12, 0x2f, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x65, 0x64, 0x49,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x26, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x56, 0x31, 0x22, 0x00, 0x12, 0x8b, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x43,
	0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x43, 0x61, 0x72, 0x64, 0x73, 0x56,
	0x31, 0x12, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x43,
	0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x4f, 0x75,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31,
	0x22, 0x00, 0x12, 0x7c, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2f, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x30, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64,
	0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00,
	0x12, 0x99, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x3c, 0x2e, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x9b, 0x01, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x3b, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x3c, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x15, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x12, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x79,
	0x50, 0x61, 0x6e, 0x12, 0x2b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x42, 0x79, 0x50, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x1a, 0x2c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42,
	0x79, 0x50, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x69,
	0x0a, 0x10, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x43, 0x61,
	0x72, 0x64, 0x12, 0x29, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x50, 0x61, 0x79, 0x49,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x11, 0x44, 0x65, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x2a,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x50, 0x61, 0x79,
	0x4f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x0d, 0x52, 0x65, 0x45, 0x6e, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x2a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x52, 0x65, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x52, 0x65, 0x45, 0x6e,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x80, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x32, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x33, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x06, 0x4e, 0x65, 0x77, 0x4b, 0x65, 0x79, 0x12, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00,
	0x12, 0x42, 0x0a, 0x0e, 0x52, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x43, 0x61, 0x72, 0x64, 0x4b, 0x65,
	0x79, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x10, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x10, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x77, 0x48, 0x61, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00,
	0x12, 0x42, 0x0a, 0x0e, 0x52, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x48, 0x61, 0x73, 0x68, 0x4b, 0x65,
	0x79, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67,
	0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_card_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_card_proto_rawDescData []byte
)

func file_inner_processing_grpc_card_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_card_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_card_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_card_proto_rawDesc), len(file_inner_processing_grpc_card_proto_rawDesc)))
	})
	return file_inner_processing_grpc_card_proto_rawDescData
}

var file_inner_processing_grpc_card_proto_msgTypes = make([]protoimpl.MessageInfo, 35)
var file_inner_processing_grpc_card_proto_goTypes = []any{
	(*CreateClientRequestV1)(nil),                  // 0: processing.card.card.CreateClientRequestV1
	(*CreateClientResponseV1)(nil),                 // 1: processing.card.card.CreateClientResponseV1
	(*CreatePayOutClientRequestV1)(nil),            // 2: processing.card.card.CreatePayOutClientRequestV1
	(*CreatePayOutClientResponseV1)(nil),           // 3: processing.card.card.CreatePayOutClientResponseV1
	(*ClientCard)(nil),                             // 4: processing.card.card.ClientCard
	(*GetCardTokensRequestV1)(nil),                 // 5: processing.card.card.GetCardTokensRequestV1
	(*GetCardTokensResponseV1)(nil),                // 6: processing.card.card.GetCardTokensResponseV1
	(*CardTokenV1)(nil),                            // 7: processing.card.card.CardTokenV1
	(*GetOneClickPayInCardsRequestV1)(nil),         // 8: processing.card.card.GetOneClickPayInCardsRequestV1
	(*GetOneClickPayInCardsResponseV1)(nil),        // 9: processing.card.card.GetOneClickPayInCardsResponseV1
	(*GetOneClickPayOutCardsRequestV1)(nil),        // 10: processing.card.card.GetOneClickPayOutCardsRequestV1
	(*GetOneClickPayOutCardsResponseV1)(nil),       // 11: processing.card.card.GetOneClickPayOutCardsResponseV1
	(*GetEncryptedCardRequestV1)(nil),              // 12: processing.card.card.GetEncryptedCardRequestV1
	(*GetEncryptedCardResponseV1)(nil),             // 13: processing.card.card.GetEncryptedCardResponseV1
	(*GetEncryptedPayOutCardResponseV1)(nil),       // 14: processing.card.card.GetEncryptedPayOutCardResponseV1
	(*GetPanByCardIdRequestV1)(nil),                // 15: processing.card.card.GetPanByCardIdRequestV1
	(*GetPanByHashedIdRequestV1)(nil),              // 16: processing.card.card.GetPanByHashedIdRequestV1
	(*GetPanResponseV1)(nil),                       // 17: processing.card.card.GetPanResponseV1
	(*GetClientListByVerificationRequestV1)(nil),   // 18: processing.card.card.GetClientListByVerificationRequestV1
	(*GetClientListByProjectClientRequestV1)(nil),  // 19: processing.card.card.GetClientListByProjectClientRequestV1
	(*ProjectAndClientData)(nil),                   // 20: processing.card.card.ProjectAndClientData
	(*GetClientListByProjectClientResponseV1)(nil), // 21: processing.card.card.GetClientListByProjectClientResponseV1
	(*GetClientListData)(nil),                      // 22: processing.card.card.GetClientListData
	(*CheckClientActivenessRequestV1)(nil),         // 23: processing.card.card.CheckClientActivenessRequestV1
	(*GetCardByPanRequestV1)(nil),                  // 24: processing.card.card.GetCardByPanRequestV1
	(*GetCardByPanResponseV1)(nil),                 // 25: processing.card.card.GetCardByPanResponseV1
	(*DecryptPayInRequest)(nil),                    // 26: processing.card.card.DecryptPayInRequest
	(*DecryptPayOutRequest)(nil),                   // 27: processing.card.card.DecryptPayOutRequest
	(*ReEncryptCardRequest)(nil),                   // 28: processing.card.card.ReEncryptCardRequest
	(*DecryptPayInResponse)(nil),                   // 29: processing.card.card.DecryptPayInResponse
	(*DecryptPayOutResponse)(nil),                  // 30: processing.card.card.DecryptPayOutResponse
	(*ReEncryptCardResponse)(nil),                  // 31: processing.card.card.ReEncryptCardResponse
	(*GetPanInfoByProjectIdRequest)(nil),           // 32: processing.card.card.GetPanInfoByProjectIdRequest
	(*GetPanInfoByProjectIdResponse)(nil),          // 33: processing.card.card.GetPanInfoByProjectIdResponse
	nil,                                            // 34: processing.card.card.GetOneClickPayOutCardsResponseV1.CardsEntry
	(*TerminalProjectV1)(nil),                      // 35: processing.acquirer.terminal.TerminalProjectV1
	(*timestamppb.Timestamp)(nil),                  // 36: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                          // 37: google.protobuf.Empty
}
var file_inner_processing_grpc_card_proto_depIdxs = []int32{
	4,  // 0: processing.card.card.CreateClientResponseV1.ClientCard:type_name -> processing.card.card.ClientCard
	4,  // 1: processing.card.card.CreatePayOutClientResponseV1.ClientCard:type_name -> processing.card.card.ClientCard
	4,  // 2: processing.card.card.GetCardTokensResponseV1.card:type_name -> processing.card.card.ClientCard
	7,  // 3: processing.card.card.GetCardTokensResponseV1.tokens:type_name -> processing.card.card.CardTokenV1
	35, // 4: processing.card.card.GetOneClickPayInCardsRequestV1.activeTerminals:type_name -> processing.acquirer.terminal.TerminalProjectV1
	13, // 5: processing.card.card.GetOneClickPayInCardsResponseV1.cards:type_name -> processing.card.card.GetEncryptedCardResponseV1
	35, // 6: processing.card.card.GetOneClickPayOutCardsRequestV1.activeTerminals:type_name -> processing.acquirer.terminal.TerminalProjectV1
	34, // 7: processing.card.card.GetOneClickPayOutCardsResponseV1.cards:type_name -> processing.card.card.GetOneClickPayOutCardsResponseV1.CardsEntry
	36, // 8: processing.card.card.GetClientListByVerificationRequestV1.client_created_at_from:type_name -> google.protobuf.Timestamp
	36, // 9: processing.card.card.GetClientListByVerificationRequestV1.client_created_at_to:type_name -> google.protobuf.Timestamp
	20, // 10: processing.card.card.GetClientListByProjectClientRequestV1.data:type_name -> processing.card.card.ProjectAndClientData
	22, // 11: processing.card.card.GetClientListByProjectClientResponseV1.data:type_name -> processing.card.card.GetClientListData
	0,  // 12: processing.card.card.Card.CreateClientV1:input_type -> processing.card.card.CreateClientRequestV1
	5,  // 13: processing.card.card.Card.GetCardTokensV1:input_type -> processing.card.card.GetCardTokensRequestV1
	8,  // 14: processing.card.card.Card.GetOneClickPayInCardsV1:input_type -> processing.card.card.GetOneClickPayInCardsRequestV1
	15, // 15: processing.card.card.Card.GetPanByCardIdV1:input_type -> processing.card.card.GetPanByCardIdRequestV1
	16, // 16: processing.card.card.Card.GetPanByHashedIdV1:input_type -> processing.card.card.GetPanByHashedIdRequestV1
	10, // 17: processing.card.card.Card.GetOneClickPayOutCardsV1:input_type -> processing.card.card.GetOneClickPayOutCardsRequestV1
	12, // 18: processing.card.card.Card.GetEncryptedCardToken:input_type -> processing.card.card.GetEncryptedCardRequestV1
	18, // 19: processing.card.card.Card.GetClientListByVerification:input_type -> processing.card.card.GetClientListByVerificationRequestV1
	19, // 20: processing.card.card.Card.GetClientListByProjectClient:input_type -> processing.card.card.GetClientListByProjectClientRequestV1
	23, // 21: processing.card.card.Card.CheckClientActiveness:input_type -> processing.card.card.CheckClientActivenessRequestV1
	24, // 22: processing.card.card.Card.GetCardByPan:input_type -> processing.card.card.GetCardByPanRequestV1
	26, // 23: processing.card.card.Card.DecryptPayInCard:input_type -> processing.card.card.DecryptPayInRequest
	27, // 24: processing.card.card.Card.DecryptPayOutCard:input_type -> processing.card.card.DecryptPayOutRequest
	28, // 25: processing.card.card.Card.ReEncryptCard:input_type -> processing.card.card.ReEncryptCardRequest
	32, // 26: processing.card.card.Card.GetPanInfoByProjectId:input_type -> processing.card.card.GetPanInfoByProjectIdRequest
	37, // 27: processing.card.card.Card.NewKey:input_type -> google.protobuf.Empty
	37, // 28: processing.card.card.Card.RotateCardKeys:input_type -> google.protobuf.Empty
	37, // 29: processing.card.card.Card.CheckExpireCards:input_type -> google.protobuf.Empty
	37, // 30: processing.card.card.Card.CreateNewHashKey:input_type -> google.protobuf.Empty
	37, // 31: processing.card.card.Card.RotateHashKeys:input_type -> google.protobuf.Empty
	1,  // 32: processing.card.card.Card.CreateClientV1:output_type -> processing.card.card.CreateClientResponseV1
	6,  // 33: processing.card.card.Card.GetCardTokensV1:output_type -> processing.card.card.GetCardTokensResponseV1
	9,  // 34: processing.card.card.Card.GetOneClickPayInCardsV1:output_type -> processing.card.card.GetOneClickPayInCardsResponseV1
	17, // 35: processing.card.card.Card.GetPanByCardIdV1:output_type -> processing.card.card.GetPanResponseV1
	17, // 36: processing.card.card.Card.GetPanByHashedIdV1:output_type -> processing.card.card.GetPanResponseV1
	11, // 37: processing.card.card.Card.GetOneClickPayOutCardsV1:output_type -> processing.card.card.GetOneClickPayOutCardsResponseV1
	13, // 38: processing.card.card.Card.GetEncryptedCardToken:output_type -> processing.card.card.GetEncryptedCardResponseV1
	21, // 39: processing.card.card.Card.GetClientListByVerification:output_type -> processing.card.card.GetClientListByProjectClientResponseV1
	21, // 40: processing.card.card.Card.GetClientListByProjectClient:output_type -> processing.card.card.GetClientListByProjectClientResponseV1
	37, // 41: processing.card.card.Card.CheckClientActiveness:output_type -> google.protobuf.Empty
	25, // 42: processing.card.card.Card.GetCardByPan:output_type -> processing.card.card.GetCardByPanResponseV1
	29, // 43: processing.card.card.Card.DecryptPayInCard:output_type -> processing.card.card.DecryptPayInResponse
	30, // 44: processing.card.card.Card.DecryptPayOutCard:output_type -> processing.card.card.DecryptPayOutResponse
	31, // 45: processing.card.card.Card.ReEncryptCard:output_type -> processing.card.card.ReEncryptCardResponse
	33, // 46: processing.card.card.Card.GetPanInfoByProjectId:output_type -> processing.card.card.GetPanInfoByProjectIdResponse
	37, // 47: processing.card.card.Card.NewKey:output_type -> google.protobuf.Empty
	37, // 48: processing.card.card.Card.RotateCardKeys:output_type -> google.protobuf.Empty
	37, // 49: processing.card.card.Card.CheckExpireCards:output_type -> google.protobuf.Empty
	37, // 50: processing.card.card.Card.CreateNewHashKey:output_type -> google.protobuf.Empty
	37, // 51: processing.card.card.Card.RotateHashKeys:output_type -> google.protobuf.Empty
	32, // [32:52] is the sub-list for method output_type
	12, // [12:32] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_card_proto_init() }
func file_inner_processing_grpc_card_proto_init() {
	if File_inner_processing_grpc_card_proto != nil {
		return
	}
	file_inner_processing_grpc_terminal_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_card_proto_rawDesc), len(file_inner_processing_grpc_card_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   35,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_card_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_card_proto_depIdxs,
		MessageInfos:      file_inner_processing_grpc_card_proto_msgTypes,
	}.Build()
	File_inner_processing_grpc_card_proto = out.File
	file_inner_processing_grpc_card_proto_goTypes = nil
	file_inner_processing_grpc_card_proto_depIdxs = nil
}
