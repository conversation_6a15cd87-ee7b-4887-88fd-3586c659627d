// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func file_inner_processing_grpc_collector_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_collector_proto_message_TimestampToZap(
	label string,
	in *timestamppb.Timestamp,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Seconds", in.GetSeconds()),
		zap.Any("Nanos", in.GetNanos()),
	)
}

func file_inner_processing_grpc_collector_proto_message_CollectTransactionRequestV1ToZap(
	label string,
	in *CollectTransactionRequestV1,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("TransactionId", in.GetTransactionId()),
		zap.Any("MerchantId", in.GetMerchantId()),
		zap.Any("ProjectClientId", in.GetProjectClientId()),
		zap.Any("Phone", in.GetPhone()),
		zap.Any("Email", in.GetEmail()),
		zap.Any("HashedPan", in.GetHashedPan()),
		zap.Any("TransactionAggregatedType", in.GetTransactionAggregatedType()),
		file_inner_processing_grpc_collector_proto_message_TimestampToZap("SessionGeneratedAt", in.GetSessionGeneratedAt()),
		file_inner_processing_grpc_collector_proto_message_TimestampToZap("SessionStartedAt", in.GetSessionStartedAt()),
		zap.Any("TransactionAmount", in.GetTransactionAmount()),
		zap.Any("TransactionCurrency", in.GetTransactionCurrency()),
		zap.Any("TransactionType", in.GetTransactionType()),
	)
}

func file_inner_processing_grpc_collector_proto_message_CollectorEmptyResponseToZap(
	label string,
	in *CollectorEmptyResponse,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

var _ CollectorServer = (*loggedCollectorServer)(nil)

func NewLoggedCollectorServer(srv CollectorServer) CollectorServer {
	return &loggedCollectorServer{srv: srv}
}

type loggedCollectorServer struct {
	UnimplementedCollectorServer

	srv CollectorServer
}

func (s *loggedCollectorServer) CollectTransaction(
	ctx context.Context,
	request *CollectTransactionRequestV1,
) (
	response *CollectorEmptyResponse,
	err error,
) {
	label := cntx.Begin(ctx, "CollectorServer_CollectTransaction")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_collector_proto_message_CollectorEmptyResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_collector_proto_message_CollectTransactionRequestV1ToZap(label+"request", request),
	)

	response, err = s.srv.CollectTransaction(ctx, request)

	return
}

func (s *loggedCollectorServer) GetTransactionStatus(
	ctx context.Context,
	request *emptypb.Empty,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CollectorServer_GetTransactionStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_collector_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_collector_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.srv.GetTransactionStatus(ctx, request)

	return
}

var _ CollectorClient = (*loggedCollectorClient)(nil)

func NewLoggedCollectorClient(client CollectorClient) CollectorClient {
	return &loggedCollectorClient{client: client}
}

type loggedCollectorClient struct {
	client CollectorClient
}

func (s *loggedCollectorClient) CollectTransaction(
	ctx context.Context,
	request *CollectTransactionRequestV1,
	opts ...grpc.CallOption,
) (
	response *CollectorEmptyResponse,
	err error,
) {
	label := cntx.Begin(ctx, "CollectorClient_CollectTransaction")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_collector_proto_message_CollectorEmptyResponseToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_collector_proto_message_CollectTransactionRequestV1ToZap(label+"request", request),
	)

	response, err = s.client.CollectTransaction(ctx, request, opts...)

	return
}

func (s *loggedCollectorClient) GetTransactionStatus(
	ctx context.Context,
	request *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "CollectorClient_GetTransactionStatus")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_collector_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_collector_proto_message_EmptyToZap(label+"request", request),
	)

	response, err = s.client.GetTransactionStatus(ctx, request, opts...)

	return
}
