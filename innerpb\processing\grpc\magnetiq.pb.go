// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/magnetiq.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EnumMagnetiqStatus int32

const (
	EnumMagnetiqStatus_MagnetiqStatusRequested  EnumMagnetiqStatus = 0
	EnumMagnetiqStatus_MagnetiqStatusDeclined   EnumMagnetiqStatus = 1
	EnumMagnetiqStatus_MagnetiqStatusApproved   EnumMagnetiqStatus = 2
	EnumMagnetiqStatus_MagnetiqStatusUnapproved EnumMagnetiqStatus = 3
	EnumMagnetiqStatus_MagnetiqStatusCancelled  EnumMagnetiqStatus = 4
	EnumMagnetiqStatus_MagnetiqStatusDeposited  EnumMagnetiqStatus = 5
	EnumMagnetiqStatus_MagnetiqStatusProcessed  EnumMagnetiqStatus = 6
	EnumMagnetiqStatus_MagnetiqStatusReversed   EnumMagnetiqStatus = 7
)

// Enum value maps for EnumMagnetiqStatus.
var (
	EnumMagnetiqStatus_name = map[int32]string{
		0: "MagnetiqStatusRequested",
		1: "MagnetiqStatusDeclined",
		2: "MagnetiqStatusApproved",
		3: "MagnetiqStatusUnapproved",
		4: "MagnetiqStatusCancelled",
		5: "MagnetiqStatusDeposited",
		6: "MagnetiqStatusProcessed",
		7: "MagnetiqStatusReversed",
	}
	EnumMagnetiqStatus_value = map[string]int32{
		"MagnetiqStatusRequested":  0,
		"MagnetiqStatusDeclined":   1,
		"MagnetiqStatusApproved":   2,
		"MagnetiqStatusUnapproved": 3,
		"MagnetiqStatusCancelled":  4,
		"MagnetiqStatusDeposited":  5,
		"MagnetiqStatusProcessed":  6,
		"MagnetiqStatusReversed":   7,
	}
)

func (x EnumMagnetiqStatus) Enum() *EnumMagnetiqStatus {
	p := new(EnumMagnetiqStatus)
	*p = x
	return p
}

func (x EnumMagnetiqStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnumMagnetiqStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_magnetiq_proto_enumTypes[0].Descriptor()
}

func (EnumMagnetiqStatus) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_magnetiq_proto_enumTypes[0]
}

func (x EnumMagnetiqStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnumMagnetiqStatus.Descriptor instead.
func (EnumMagnetiqStatus) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_magnetiq_proto_rawDescGZIP(), []int{0}
}

type MagnetiqFaultErrCode int32

const (
	MagnetiqFaultErrCode_MagnetiqFaultServerErr                            MagnetiqFaultErrCode = 0
	MagnetiqFaultErrCode_MagnetiqFaultLockTimeoutErr                       MagnetiqFaultErrCode = 1
	MagnetiqFaultErrCode_MagnetiqFaultEntityNotFoundErr                    MagnetiqFaultErrCode = 2
	MagnetiqFaultErrCode_MagnetiqFaultBadRequestErr                        MagnetiqFaultErrCode = 3
	MagnetiqFaultErrCode_MagnetiqFaultIntegrityViolationErr                MagnetiqFaultErrCode = 4
	MagnetiqFaultErrCode_MagnetiqFaultOrderIdMustBeUniqueErr               MagnetiqFaultErrCode = 5
	MagnetiqFaultErrCode_MagnetiqFaultTokenAlreadyRegisteredErr            MagnetiqFaultErrCode = 6
	MagnetiqFaultErrCode_MagnetiqFaultTokenNotFoundErr                     MagnetiqFaultErrCode = 7
	MagnetiqFaultErrCode_MagnetiqFaultDirectoryServiceNotFoundErr          MagnetiqFaultErrCode = 8
	MagnetiqFaultErrCode_MagnetiqFaultKeyNotFoundErr                       MagnetiqFaultErrCode = 9
	MagnetiqFaultErrCode_MagnetiqFaultRecurringNotFoundErr                 MagnetiqFaultErrCode = 10
	MagnetiqFaultErrCode_MagnetiqFaultPaymentStateFlowViolationErr         MagnetiqFaultErrCode = 11
	MagnetiqFaultErrCode_MagnetiqFaultAuthNon3dPaymentErr                  MagnetiqFaultErrCode = 12
	MagnetiqFaultErrCode_MagnetiqFaultAuthenticatePaymentErr               MagnetiqFaultErrCode = 13
	MagnetiqFaultErrCode_MagnetiqFaultAuthenticateNon3dPaymentErr          MagnetiqFaultErrCode = 14
	MagnetiqFaultErrCode_MagnetiqFaultDepositPaymentErr                    MagnetiqFaultErrCode = 15
	MagnetiqFaultErrCode_MagnetiqFaultCancelPaymentErr                     MagnetiqFaultErrCode = 16
	MagnetiqFaultErrCode_MagnetiqFaultCardNameRequiredErr                  MagnetiqFaultErrCode = 17
	MagnetiqFaultErrCode_MagnetiqFaultCardNumberRequiredErr                MagnetiqFaultErrCode = 18
	MagnetiqFaultErrCode_MagnetiqFaultCardExpiryRequiredErr                MagnetiqFaultErrCode = 19
	MagnetiqFaultErrCode_MagnetiqFaultInvalidCardExpiryDateErr             MagnetiqFaultErrCode = 20
	MagnetiqFaultErrCode_MagnetiqFaultInvalidCardNumberErr                 MagnetiqFaultErrCode = 21
	MagnetiqFaultErrCode_MagnetiqFaultCouldNotReversePaymentErr            MagnetiqFaultErrCode = 22
	MagnetiqFaultErrCode_MagnetiqFaultMerchantInterfaceClosedErr           MagnetiqFaultErrCode = 23
	MagnetiqFaultErrCode_MagnetiqFaultInvalidCurrencyErr                   MagnetiqFaultErrCode = 24
	MagnetiqFaultErrCode_MagnetiqFaultOriginalCreditNotAllowedErr          MagnetiqFaultErrCode = 25
	MagnetiqFaultErrCode_MagnetiqFaultCSCRequiredErr                       MagnetiqFaultErrCode = 26
	MagnetiqFaultErrCode_MagnetiqFaultCSCInvalidErr                        MagnetiqFaultErrCode = 27
	MagnetiqFaultErrCode_MagnetiqFaultPaymentModeRequiredErr               MagnetiqFaultErrCode = 28
	MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderRequiredErr              MagnetiqFaultErrCode = 29
	MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderIDRequiredErr            MagnetiqFaultErrCode = 30
	MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderAmountRequiredErr        MagnetiqFaultErrCode = 31
	MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderCurrencyRequiredErr      MagnetiqFaultErrCode = 32
	MagnetiqFaultErrCode_MagnetiqFaultPaymentOrderDescriptionRequiredErr   MagnetiqFaultErrCode = 33
	MagnetiqFaultErrCode_MagnetiqFaultD3DACSParResRequiredErr              MagnetiqFaultErrCode = 34
	MagnetiqFaultErrCode_MagnetiqFaultRecurringExpiredErr                  MagnetiqFaultErrCode = 35
	MagnetiqFaultErrCode_MagnetiqFaultTooFrequentRecurringErr              MagnetiqFaultErrCode = 36
	MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateRequiredErr          MagnetiqFaultErrCode = 37
	MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyRequiredErr        MagnetiqFaultErrCode = 38
	MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateIncorrectErr         MagnetiqFaultErrCode = 39
	MagnetiqFaultErrCode_MagnetiqFaultRecurringEndDateFutureErr            MagnetiqFaultErrCode = 40
	MagnetiqFaultErrCode_MagnetiqFaultRecurringFrequencyGreaterThanZeroErr MagnetiqFaultErrCode = 41
	MagnetiqFaultErrCode_MagnetiqFaultRecurringIDRequiredErr               MagnetiqFaultErrCode = 42
	MagnetiqFaultErrCode_MagnetiqFaultRemoteAddressRequiredErr             MagnetiqFaultErrCode = 43
	MagnetiqFaultErrCode_MagnetiqFaultInvalidSignatureErr                  MagnetiqFaultErrCode = 44
	MagnetiqFaultErrCode_MagnetiqFaultRequestRequiredErr                   MagnetiqFaultErrCode = 45
	MagnetiqFaultErrCode_MagnetiqFaultInterfaceRequiredErr                 MagnetiqFaultErrCode = 46
	MagnetiqFaultErrCode_MagnetiqFaultKeyIndexRequiredErr                  MagnetiqFaultErrCode = 47
	MagnetiqFaultErrCode_MagnetiqFaultKeyRequiredErr                       MagnetiqFaultErrCode = 48
	MagnetiqFaultErrCode_MagnetiqFaultDataRequiredErr                      MagnetiqFaultErrCode = 49
	MagnetiqFaultErrCode_MagnetiqFaultSignatureRequiredErr                 MagnetiqFaultErrCode = 50
	MagnetiqFaultErrCode_MagnetiqFaultXMLDeserializationFailedErr          MagnetiqFaultErrCode = 51
	MagnetiqFaultErrCode_MagnetiqFaultXMLSerializationFailedErr            MagnetiqFaultErrCode = 52
	MagnetiqFaultErrCode_MagnetiqFaultDataDecryptionFailedErr              MagnetiqFaultErrCode = 53
	MagnetiqFaultErrCode_MagnetiqFaultDataEncryptionFailedErr              MagnetiqFaultErrCode = 54
	MagnetiqFaultErrCode_MagnetiqFaultSignatureVerificationFailedErr       MagnetiqFaultErrCode = 55
	MagnetiqFaultErrCode_MagnetiqFaultSigningFailedErr                     MagnetiqFaultErrCode = 56
	MagnetiqFaultErrCode_MagnetiqFaultPaymentNotFoundErr                   MagnetiqFaultErrCode = 57
	MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeAuthenticationErr  MagnetiqFaultErrCode = 58
	MagnetiqFaultErrCode_MagnetiqFaultPaymentRequiredErr                   MagnetiqFaultErrCode = 59
	MagnetiqFaultErrCode_MagnetiqFaultInvalidPaymentModeCreationErr        MagnetiqFaultErrCode = 60
	MagnetiqFaultErrCode_MagnetiqFaultTokensDisabledErr                    MagnetiqFaultErrCode = 61
	MagnetiqFaultErrCode_MagnetiqFaultInvalidTokenTypeErr                  MagnetiqFaultErrCode = 62
	MagnetiqFaultErrCode_MagnetiqFaultUnableDecryptTokenErr                MagnetiqFaultErrCode = 63
	MagnetiqFaultErrCode_MagnetiqFaultIncorrectAmountErr                   MagnetiqFaultErrCode = 64
	MagnetiqFaultErrCode_MagnetiqFaultIncorrectCurrencyErr                 MagnetiqFaultErrCode = 65
	MagnetiqFaultErrCode_MagnetiqFaultIncorrectCryptogramErr               MagnetiqFaultErrCode = 66
	MagnetiqFaultErrCode_MagnetiqFaultIncorrectTokenModeErr                MagnetiqFaultErrCode = 67
)

// Enum value maps for MagnetiqFaultErrCode.
var (
	MagnetiqFaultErrCode_name = map[int32]string{
		0:  "MagnetiqFaultServerErr",
		1:  "MagnetiqFaultLockTimeoutErr",
		2:  "MagnetiqFaultEntityNotFoundErr",
		3:  "MagnetiqFaultBadRequestErr",
		4:  "MagnetiqFaultIntegrityViolationErr",
		5:  "MagnetiqFaultOrderIdMustBeUniqueErr",
		6:  "MagnetiqFaultTokenAlreadyRegisteredErr",
		7:  "MagnetiqFaultTokenNotFoundErr",
		8:  "MagnetiqFaultDirectoryServiceNotFoundErr",
		9:  "MagnetiqFaultKeyNotFoundErr",
		10: "MagnetiqFaultRecurringNotFoundErr",
		11: "MagnetiqFaultPaymentStateFlowViolationErr",
		12: "MagnetiqFaultAuthNon3dPaymentErr",
		13: "MagnetiqFaultAuthenticatePaymentErr",
		14: "MagnetiqFaultAuthenticateNon3dPaymentErr",
		15: "MagnetiqFaultDepositPaymentErr",
		16: "MagnetiqFaultCancelPaymentErr",
		17: "MagnetiqFaultCardNameRequiredErr",
		18: "MagnetiqFaultCardNumberRequiredErr",
		19: "MagnetiqFaultCardExpiryRequiredErr",
		20: "MagnetiqFaultInvalidCardExpiryDateErr",
		21: "MagnetiqFaultInvalidCardNumberErr",
		22: "MagnetiqFaultCouldNotReversePaymentErr",
		23: "MagnetiqFaultMerchantInterfaceClosedErr",
		24: "MagnetiqFaultInvalidCurrencyErr",
		25: "MagnetiqFaultOriginalCreditNotAllowedErr",
		26: "MagnetiqFaultCSCRequiredErr",
		27: "MagnetiqFaultCSCInvalidErr",
		28: "MagnetiqFaultPaymentModeRequiredErr",
		29: "MagnetiqFaultPaymentOrderRequiredErr",
		30: "MagnetiqFaultPaymentOrderIDRequiredErr",
		31: "MagnetiqFaultPaymentOrderAmountRequiredErr",
		32: "MagnetiqFaultPaymentOrderCurrencyRequiredErr",
		33: "MagnetiqFaultPaymentOrderDescriptionRequiredErr",
		34: "MagnetiqFaultD3DACSParResRequiredErr",
		35: "MagnetiqFaultRecurringExpiredErr",
		36: "MagnetiqFaultTooFrequentRecurringErr",
		37: "MagnetiqFaultRecurringEndDateRequiredErr",
		38: "MagnetiqFaultRecurringFrequencyRequiredErr",
		39: "MagnetiqFaultRecurringEndDateIncorrectErr",
		40: "MagnetiqFaultRecurringEndDateFutureErr",
		41: "MagnetiqFaultRecurringFrequencyGreaterThanZeroErr",
		42: "MagnetiqFaultRecurringIDRequiredErr",
		43: "MagnetiqFaultRemoteAddressRequiredErr",
		44: "MagnetiqFaultInvalidSignatureErr",
		45: "MagnetiqFaultRequestRequiredErr",
		46: "MagnetiqFaultInterfaceRequiredErr",
		47: "MagnetiqFaultKeyIndexRequiredErr",
		48: "MagnetiqFaultKeyRequiredErr",
		49: "MagnetiqFaultDataRequiredErr",
		50: "MagnetiqFaultSignatureRequiredErr",
		51: "MagnetiqFaultXMLDeserializationFailedErr",
		52: "MagnetiqFaultXMLSerializationFailedErr",
		53: "MagnetiqFaultDataDecryptionFailedErr",
		54: "MagnetiqFaultDataEncryptionFailedErr",
		55: "MagnetiqFaultSignatureVerificationFailedErr",
		56: "MagnetiqFaultSigningFailedErr",
		57: "MagnetiqFaultPaymentNotFoundErr",
		58: "MagnetiqFaultInvalidPaymentModeAuthenticationErr",
		59: "MagnetiqFaultPaymentRequiredErr",
		60: "MagnetiqFaultInvalidPaymentModeCreationErr",
		61: "MagnetiqFaultTokensDisabledErr",
		62: "MagnetiqFaultInvalidTokenTypeErr",
		63: "MagnetiqFaultUnableDecryptTokenErr",
		64: "MagnetiqFaultIncorrectAmountErr",
		65: "MagnetiqFaultIncorrectCurrencyErr",
		66: "MagnetiqFaultIncorrectCryptogramErr",
		67: "MagnetiqFaultIncorrectTokenModeErr",
	}
	MagnetiqFaultErrCode_value = map[string]int32{
		"MagnetiqFaultServerErr":                            0,
		"MagnetiqFaultLockTimeoutErr":                       1,
		"MagnetiqFaultEntityNotFoundErr":                    2,
		"MagnetiqFaultBadRequestErr":                        3,
		"MagnetiqFaultIntegrityViolationErr":                4,
		"MagnetiqFaultOrderIdMustBeUniqueErr":               5,
		"MagnetiqFaultTokenAlreadyRegisteredErr":            6,
		"MagnetiqFaultTokenNotFoundErr":                     7,
		"MagnetiqFaultDirectoryServiceNotFoundErr":          8,
		"MagnetiqFaultKeyNotFoundErr":                       9,
		"MagnetiqFaultRecurringNotFoundErr":                 10,
		"MagnetiqFaultPaymentStateFlowViolationErr":         11,
		"MagnetiqFaultAuthNon3dPaymentErr":                  12,
		"MagnetiqFaultAuthenticatePaymentErr":               13,
		"MagnetiqFaultAuthenticateNon3dPaymentErr":          14,
		"MagnetiqFaultDepositPaymentErr":                    15,
		"MagnetiqFaultCancelPaymentErr":                     16,
		"MagnetiqFaultCardNameRequiredErr":                  17,
		"MagnetiqFaultCardNumberRequiredErr":                18,
		"MagnetiqFaultCardExpiryRequiredErr":                19,
		"MagnetiqFaultInvalidCardExpiryDateErr":             20,
		"MagnetiqFaultInvalidCardNumberErr":                 21,
		"MagnetiqFaultCouldNotReversePaymentErr":            22,
		"MagnetiqFaultMerchantInterfaceClosedErr":           23,
		"MagnetiqFaultInvalidCurrencyErr":                   24,
		"MagnetiqFaultOriginalCreditNotAllowedErr":          25,
		"MagnetiqFaultCSCRequiredErr":                       26,
		"MagnetiqFaultCSCInvalidErr":                        27,
		"MagnetiqFaultPaymentModeRequiredErr":               28,
		"MagnetiqFaultPaymentOrderRequiredErr":              29,
		"MagnetiqFaultPaymentOrderIDRequiredErr":            30,
		"MagnetiqFaultPaymentOrderAmountRequiredErr":        31,
		"MagnetiqFaultPaymentOrderCurrencyRequiredErr":      32,
		"MagnetiqFaultPaymentOrderDescriptionRequiredErr":   33,
		"MagnetiqFaultD3DACSParResRequiredErr":              34,
		"MagnetiqFaultRecurringExpiredErr":                  35,
		"MagnetiqFaultTooFrequentRecurringErr":              36,
		"MagnetiqFaultRecurringEndDateRequiredErr":          37,
		"MagnetiqFaultRecurringFrequencyRequiredErr":        38,
		"MagnetiqFaultRecurringEndDateIncorrectErr":         39,
		"MagnetiqFaultRecurringEndDateFutureErr":            40,
		"MagnetiqFaultRecurringFrequencyGreaterThanZeroErr": 41,
		"MagnetiqFaultRecurringIDRequiredErr":               42,
		"MagnetiqFaultRemoteAddressRequiredErr":             43,
		"MagnetiqFaultInvalidSignatureErr":                  44,
		"MagnetiqFaultRequestRequiredErr":                   45,
		"MagnetiqFaultInterfaceRequiredErr":                 46,
		"MagnetiqFaultKeyIndexRequiredErr":                  47,
		"MagnetiqFaultKeyRequiredErr":                       48,
		"MagnetiqFaultDataRequiredErr":                      49,
		"MagnetiqFaultSignatureRequiredErr":                 50,
		"MagnetiqFaultXMLDeserializationFailedErr":          51,
		"MagnetiqFaultXMLSerializationFailedErr":            52,
		"MagnetiqFaultDataDecryptionFailedErr":              53,
		"MagnetiqFaultDataEncryptionFailedErr":              54,
		"MagnetiqFaultSignatureVerificationFailedErr":       55,
		"MagnetiqFaultSigningFailedErr":                     56,
		"MagnetiqFaultPaymentNotFoundErr":                   57,
		"MagnetiqFaultInvalidPaymentModeAuthenticationErr":  58,
		"MagnetiqFaultPaymentRequiredErr":                   59,
		"MagnetiqFaultInvalidPaymentModeCreationErr":        60,
		"MagnetiqFaultTokensDisabledErr":                    61,
		"MagnetiqFaultInvalidTokenTypeErr":                  62,
		"MagnetiqFaultUnableDecryptTokenErr":                63,
		"MagnetiqFaultIncorrectAmountErr":                   64,
		"MagnetiqFaultIncorrectCurrencyErr":                 65,
		"MagnetiqFaultIncorrectCryptogramErr":               66,
		"MagnetiqFaultIncorrectTokenModeErr":                67,
	}
)

func (x MagnetiqFaultErrCode) Enum() *MagnetiqFaultErrCode {
	p := new(MagnetiqFaultErrCode)
	*p = x
	return p
}

func (x MagnetiqFaultErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MagnetiqFaultErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_magnetiq_proto_enumTypes[1].Descriptor()
}

func (MagnetiqFaultErrCode) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_magnetiq_proto_enumTypes[1]
}

func (x MagnetiqFaultErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MagnetiqFaultErrCode.Descriptor instead.
func (MagnetiqFaultErrCode) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_magnetiq_proto_rawDescGZIP(), []int{1}
}

type EnumMagnetiqActionCode int32

const (
	EnumMagnetiqActionCode_MagnetiqActionCodeApproved                             EnumMagnetiqActionCode = 0
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineGeneral                       EnumMagnetiqActionCode = 1
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExpiredCard                   EnumMagnetiqActionCode = 2
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedFraud                EnumMagnetiqActionCode = 3
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineContactAcquirer               EnumMagnetiqActionCode = 4
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineRestrictedCard                EnumMagnetiqActionCode = 5
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCallSecurityDept              EnumMagnetiqActionCode = 6
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineReferToIssuer                 EnumMagnetiqActionCode = 7
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSpecialConditions       EnumMagnetiqActionCode = 8
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidMerchant               EnumMagnetiqActionCode = 9
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidAmount                 EnumMagnetiqActionCode = 10
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidCardNumber             EnumMagnetiqActionCode = 11
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineUnacceptableFee               EnumMagnetiqActionCode = 12
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoAccountType                 EnumMagnetiqActionCode = 13
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFunctionNotSupported          EnumMagnetiqActionCode = 14
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInsufficientFunds             EnumMagnetiqActionCode = 15
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCardRecord                  EnumMagnetiqActionCode = 16
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToCardholder      EnumMagnetiqActionCode = 17
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotPermittedToTerminal        EnumMagnetiqActionCode = 18
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalLimit        EnumMagnetiqActionCode = 19
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSecurityViolation             EnumMagnetiqActionCode = 20
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineExceedsWithdrawalFrequency    EnumMagnetiqActionCode = 21
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineViolationOfLaw                EnumMagnetiqActionCode = 22
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineCardNotEffective              EnumMagnetiqActionCode = 23
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSuspectedCounterfeitCard      EnumMagnetiqActionCode = 24
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineAdditionalAuthRequired        EnumMagnetiqActionCode = 25
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineWrongOnlinePinRetry           EnumMagnetiqActionCode = 26
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineLifecycleViolation            EnumMagnetiqActionCode = 27
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclinePolicyViolation               EnumMagnetiqActionCode = 28
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFraudSecurityViolation        EnumMagnetiqActionCode = 29
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineByCardholderWish              EnumMagnetiqActionCode = 30
	EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedNoLiability        EnumMagnetiqActionCode = 31
	EnumMagnetiqActionCode_MagnetiqActionCodeAdviceAcknowledgedLiabilityAccepted  EnumMagnetiqActionCode = 32
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineInvalidTransaction            EnumMagnetiqActionCode = 33
	EnumMagnetiqActionCode_MagnetiqActionCodeStatusReEnterTransaction             EnumMagnetiqActionCode = 34
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineFormatError                   EnumMagnetiqActionCode = 35
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerOrSwitchInoperative     EnumMagnetiqActionCode = 36
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDestinationNotFound           EnumMagnetiqActionCode = 37
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineSystemMalfunction             EnumMagnetiqActionCode = 38
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerSignedOff               EnumMagnetiqActionCode = 39
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerTimedOut                EnumMagnetiqActionCode = 40
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineIssuerUnavailable             EnumMagnetiqActionCode = 41
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineDuplicateTransmission         EnumMagnetiqActionCode = 42
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNotTraceableToOriginal        EnumMagnetiqActionCode = 43
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineNoCommKeysAvailable           EnumMagnetiqActionCode = 44
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineMessageOutOfSequence          EnumMagnetiqActionCode = 45
	EnumMagnetiqActionCode_MagnetiqActionCodeStatusRequestInProgress              EnumMagnetiqActionCode = 46
	EnumMagnetiqActionCode_MagnetiqActionCodeDeclineBusinessViolation             EnumMagnetiqActionCode = 47
	EnumMagnetiqActionCode_MagnetiqActionCode3DSecureUnavailable                  EnumMagnetiqActionCode = 48
	EnumMagnetiqActionCode_MagnetiqActionCode3DSecureAuthFailed                   EnumMagnetiqActionCode = 49
	EnumMagnetiqActionCode_MagnetiqActionCodeAgreementNotObtained                 EnumMagnetiqActionCode = 50
	EnumMagnetiqActionCode_MagnetiqActionCodeAgreementSuspendedOrClosed           EnumMagnetiqActionCode = 51
	EnumMagnetiqActionCode_MagnetiqActionCodeOriginalCreditIneligible             EnumMagnetiqActionCode = 52
	EnumMagnetiqActionCode_MagnetiqActionCode3DSecureEnrollmentVerificationFailed EnumMagnetiqActionCode = 53
	EnumMagnetiqActionCode_MagnetiqActionCode3DSecurePaResExtractionFailed        EnumMagnetiqActionCode = 54
	EnumMagnetiqActionCode_MagnetiqActionCodeSuspectedFraud                       EnumMagnetiqActionCode = 55
	EnumMagnetiqActionCode_MagnetiqActionCodeAbandonedTimeout                     EnumMagnetiqActionCode = 56
	EnumMagnetiqActionCode_MagnetiqActionCodeLimitsExceeded                       EnumMagnetiqActionCode = 57
)

// Enum value maps for EnumMagnetiqActionCode.
var (
	EnumMagnetiqActionCode_name = map[int32]string{
		0:  "MagnetiqActionCodeApproved",
		1:  "MagnetiqActionCodeDeclineGeneral",
		2:  "MagnetiqActionCodeDeclineExpiredCard",
		3:  "MagnetiqActionCodeDeclineSuspectedFraud",
		4:  "MagnetiqActionCodeDeclineContactAcquirer",
		5:  "MagnetiqActionCodeDeclineRestrictedCard",
		6:  "MagnetiqActionCodeDeclineCallSecurityDept",
		7:  "MagnetiqActionCodeDeclineReferToIssuer",
		8:  "MagnetiqActionCodeDeclineIssuerSpecialConditions",
		9:  "MagnetiqActionCodeDeclineInvalidMerchant",
		10: "MagnetiqActionCodeDeclineInvalidAmount",
		11: "MagnetiqActionCodeDeclineInvalidCardNumber",
		12: "MagnetiqActionCodeDeclineUnacceptableFee",
		13: "MagnetiqActionCodeDeclineNoAccountType",
		14: "MagnetiqActionCodeDeclineFunctionNotSupported",
		15: "MagnetiqActionCodeDeclineInsufficientFunds",
		16: "MagnetiqActionCodeDeclineNoCardRecord",
		17: "MagnetiqActionCodeDeclineNotPermittedToCardholder",
		18: "MagnetiqActionCodeDeclineNotPermittedToTerminal",
		19: "MagnetiqActionCodeDeclineExceedsWithdrawalLimit",
		20: "MagnetiqActionCodeDeclineSecurityViolation",
		21: "MagnetiqActionCodeDeclineExceedsWithdrawalFrequency",
		22: "MagnetiqActionCodeDeclineViolationOfLaw",
		23: "MagnetiqActionCodeDeclineCardNotEffective",
		24: "MagnetiqActionCodeDeclineSuspectedCounterfeitCard",
		25: "MagnetiqActionCodeDeclineAdditionalAuthRequired",
		26: "MagnetiqActionCodeDeclineWrongOnlinePinRetry",
		27: "MagnetiqActionCodeDeclineLifecycleViolation",
		28: "MagnetiqActionCodeDeclinePolicyViolation",
		29: "MagnetiqActionCodeDeclineFraudSecurityViolation",
		30: "MagnetiqActionCodeDeclineByCardholderWish",
		31: "MagnetiqActionCodeAdviceAcknowledgedNoLiability",
		32: "MagnetiqActionCodeAdviceAcknowledgedLiabilityAccepted",
		33: "MagnetiqActionCodeDeclineInvalidTransaction",
		34: "MagnetiqActionCodeStatusReEnterTransaction",
		35: "MagnetiqActionCodeDeclineFormatError",
		36: "MagnetiqActionCodeDeclineIssuerOrSwitchInoperative",
		37: "MagnetiqActionCodeDeclineDestinationNotFound",
		38: "MagnetiqActionCodeDeclineSystemMalfunction",
		39: "MagnetiqActionCodeDeclineIssuerSignedOff",
		40: "MagnetiqActionCodeDeclineIssuerTimedOut",
		41: "MagnetiqActionCodeDeclineIssuerUnavailable",
		42: "MagnetiqActionCodeDeclineDuplicateTransmission",
		43: "MagnetiqActionCodeDeclineNotTraceableToOriginal",
		44: "MagnetiqActionCodeDeclineNoCommKeysAvailable",
		45: "MagnetiqActionCodeDeclineMessageOutOfSequence",
		46: "MagnetiqActionCodeStatusRequestInProgress",
		47: "MagnetiqActionCodeDeclineBusinessViolation",
		48: "MagnetiqActionCode3DSecureUnavailable",
		49: "MagnetiqActionCode3DSecureAuthFailed",
		50: "MagnetiqActionCodeAgreementNotObtained",
		51: "MagnetiqActionCodeAgreementSuspendedOrClosed",
		52: "MagnetiqActionCodeOriginalCreditIneligible",
		53: "MagnetiqActionCode3DSecureEnrollmentVerificationFailed",
		54: "MagnetiqActionCode3DSecurePaResExtractionFailed",
		55: "MagnetiqActionCodeSuspectedFraud",
		56: "MagnetiqActionCodeAbandonedTimeout",
		57: "MagnetiqActionCodeLimitsExceeded",
	}
	EnumMagnetiqActionCode_value = map[string]int32{
		"MagnetiqActionCodeApproved":                             0,
		"MagnetiqActionCodeDeclineGeneral":                       1,
		"MagnetiqActionCodeDeclineExpiredCard":                   2,
		"MagnetiqActionCodeDeclineSuspectedFraud":                3,
		"MagnetiqActionCodeDeclineContactAcquirer":               4,
		"MagnetiqActionCodeDeclineRestrictedCard":                5,
		"MagnetiqActionCodeDeclineCallSecurityDept":              6,
		"MagnetiqActionCodeDeclineReferToIssuer":                 7,
		"MagnetiqActionCodeDeclineIssuerSpecialConditions":       8,
		"MagnetiqActionCodeDeclineInvalidMerchant":               9,
		"MagnetiqActionCodeDeclineInvalidAmount":                 10,
		"MagnetiqActionCodeDeclineInvalidCardNumber":             11,
		"MagnetiqActionCodeDeclineUnacceptableFee":               12,
		"MagnetiqActionCodeDeclineNoAccountType":                 13,
		"MagnetiqActionCodeDeclineFunctionNotSupported":          14,
		"MagnetiqActionCodeDeclineInsufficientFunds":             15,
		"MagnetiqActionCodeDeclineNoCardRecord":                  16,
		"MagnetiqActionCodeDeclineNotPermittedToCardholder":      17,
		"MagnetiqActionCodeDeclineNotPermittedToTerminal":        18,
		"MagnetiqActionCodeDeclineExceedsWithdrawalLimit":        19,
		"MagnetiqActionCodeDeclineSecurityViolation":             20,
		"MagnetiqActionCodeDeclineExceedsWithdrawalFrequency":    21,
		"MagnetiqActionCodeDeclineViolationOfLaw":                22,
		"MagnetiqActionCodeDeclineCardNotEffective":              23,
		"MagnetiqActionCodeDeclineSuspectedCounterfeitCard":      24,
		"MagnetiqActionCodeDeclineAdditionalAuthRequired":        25,
		"MagnetiqActionCodeDeclineWrongOnlinePinRetry":           26,
		"MagnetiqActionCodeDeclineLifecycleViolation":            27,
		"MagnetiqActionCodeDeclinePolicyViolation":               28,
		"MagnetiqActionCodeDeclineFraudSecurityViolation":        29,
		"MagnetiqActionCodeDeclineByCardholderWish":              30,
		"MagnetiqActionCodeAdviceAcknowledgedNoLiability":        31,
		"MagnetiqActionCodeAdviceAcknowledgedLiabilityAccepted":  32,
		"MagnetiqActionCodeDeclineInvalidTransaction":            33,
		"MagnetiqActionCodeStatusReEnterTransaction":             34,
		"MagnetiqActionCodeDeclineFormatError":                   35,
		"MagnetiqActionCodeDeclineIssuerOrSwitchInoperative":     36,
		"MagnetiqActionCodeDeclineDestinationNotFound":           37,
		"MagnetiqActionCodeDeclineSystemMalfunction":             38,
		"MagnetiqActionCodeDeclineIssuerSignedOff":               39,
		"MagnetiqActionCodeDeclineIssuerTimedOut":                40,
		"MagnetiqActionCodeDeclineIssuerUnavailable":             41,
		"MagnetiqActionCodeDeclineDuplicateTransmission":         42,
		"MagnetiqActionCodeDeclineNotTraceableToOriginal":        43,
		"MagnetiqActionCodeDeclineNoCommKeysAvailable":           44,
		"MagnetiqActionCodeDeclineMessageOutOfSequence":          45,
		"MagnetiqActionCodeStatusRequestInProgress":              46,
		"MagnetiqActionCodeDeclineBusinessViolation":             47,
		"MagnetiqActionCode3DSecureUnavailable":                  48,
		"MagnetiqActionCode3DSecureAuthFailed":                   49,
		"MagnetiqActionCodeAgreementNotObtained":                 50,
		"MagnetiqActionCodeAgreementSuspendedOrClosed":           51,
		"MagnetiqActionCodeOriginalCreditIneligible":             52,
		"MagnetiqActionCode3DSecureEnrollmentVerificationFailed": 53,
		"MagnetiqActionCode3DSecurePaResExtractionFailed":        54,
		"MagnetiqActionCodeSuspectedFraud":                       55,
		"MagnetiqActionCodeAbandonedTimeout":                     56,
		"MagnetiqActionCodeLimitsExceeded":                       57,
	}
)

func (x EnumMagnetiqActionCode) Enum() *EnumMagnetiqActionCode {
	p := new(EnumMagnetiqActionCode)
	*p = x
	return p
}

func (x EnumMagnetiqActionCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnumMagnetiqActionCode) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_magnetiq_proto_enumTypes[2].Descriptor()
}

func (EnumMagnetiqActionCode) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_magnetiq_proto_enumTypes[2]
}

func (x EnumMagnetiqActionCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnumMagnetiqActionCode.Descriptor instead.
func (EnumMagnetiqActionCode) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_magnetiq_proto_rawDescGZIP(), []int{2}
}

type EnumMagnetiqModeCode int32

const (
	EnumMagnetiqModeCode_MagnetiqModeNone3DS       EnumMagnetiqModeCode = 0
	EnumMagnetiqModeCode_MagnetiqMode3DS           EnumMagnetiqModeCode = 1
	EnumMagnetiqModeCode_MagnetiqModeOneClickPayIn EnumMagnetiqModeCode = 2
)

// Enum value maps for EnumMagnetiqModeCode.
var (
	EnumMagnetiqModeCode_name = map[int32]string{
		0: "MagnetiqModeNone3DS",
		1: "MagnetiqMode3DS",
		2: "MagnetiqModeOneClickPayIn",
	}
	EnumMagnetiqModeCode_value = map[string]int32{
		"MagnetiqModeNone3DS":       0,
		"MagnetiqMode3DS":           1,
		"MagnetiqModeOneClickPayIn": 2,
	}
)

func (x EnumMagnetiqModeCode) Enum() *EnumMagnetiqModeCode {
	p := new(EnumMagnetiqModeCode)
	*p = x
	return p
}

func (x EnumMagnetiqModeCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnumMagnetiqModeCode) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_magnetiq_proto_enumTypes[3].Descriptor()
}

func (EnumMagnetiqModeCode) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_magnetiq_proto_enumTypes[3]
}

func (x EnumMagnetiqModeCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnumMagnetiqModeCode.Descriptor instead.
func (EnumMagnetiqModeCode) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_magnetiq_proto_rawDescGZIP(), []int{3}
}

type MagnetiqStatusRef struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Code              *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Name              *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *MagnetiqStatusRef) Reset() {
	*x = MagnetiqStatusRef{}
	mi := &file_inner_processing_grpc_magnetiq_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MagnetiqStatusRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MagnetiqStatusRef) ProtoMessage() {}

func (x *MagnetiqStatusRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_magnetiq_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MagnetiqStatusRef.ProtoReflect.Descriptor instead.
func (*MagnetiqStatusRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_magnetiq_proto_rawDescGZIP(), []int{0}
}

func (x *MagnetiqStatusRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *MagnetiqStatusRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *MagnetiqStatusRef) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

type MagnetiqFaultErrCodeRef struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Message          *string                `protobuf:"bytes,1,opt,name=message" json:"message,omitempty"`
	IntegrationError *IntegrationError      `protobuf:"varint,2,opt,name=integration_error,json=integrationError,enum=processing.integration.integration.IntegrationError" json:"integration_error,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *MagnetiqFaultErrCodeRef) Reset() {
	*x = MagnetiqFaultErrCodeRef{}
	mi := &file_inner_processing_grpc_magnetiq_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MagnetiqFaultErrCodeRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MagnetiqFaultErrCodeRef) ProtoMessage() {}

func (x *MagnetiqFaultErrCodeRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_magnetiq_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MagnetiqFaultErrCodeRef.ProtoReflect.Descriptor instead.
func (*MagnetiqFaultErrCodeRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_magnetiq_proto_rawDescGZIP(), []int{1}
}

func (x *MagnetiqFaultErrCodeRef) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *MagnetiqFaultErrCodeRef) GetIntegrationError() IntegrationError {
	if x != nil && x.IntegrationError != nil {
		return *x.IntegrationError
	}
	return IntegrationError_None
}

type MagnetiqActionCodeRef struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Message          *string                `protobuf:"bytes,1,opt,name=message" json:"message,omitempty"`
	IntegrationError *IntegrationError      `protobuf:"varint,2,opt,name=integration_error,json=integrationError,enum=processing.integration.integration.IntegrationError" json:"integration_error,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *MagnetiqActionCodeRef) Reset() {
	*x = MagnetiqActionCodeRef{}
	mi := &file_inner_processing_grpc_magnetiq_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MagnetiqActionCodeRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MagnetiqActionCodeRef) ProtoMessage() {}

func (x *MagnetiqActionCodeRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_magnetiq_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MagnetiqActionCodeRef.ProtoReflect.Descriptor instead.
func (*MagnetiqActionCodeRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_magnetiq_proto_rawDescGZIP(), []int{2}
}

func (x *MagnetiqActionCodeRef) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *MagnetiqActionCodeRef) GetIntegrationError() IntegrationError {
	if x != nil && x.IntegrationError != nil {
		return *x.IntegrationError
	}
	return IntegrationError_None
}

type MagnetiqModeRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *string                `protobuf:"bytes,1,opt,name=code" json:"code,omitempty"`
	Description   *string                `protobuf:"bytes,2,opt,name=description" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MagnetiqModeRef) Reset() {
	*x = MagnetiqModeRef{}
	mi := &file_inner_processing_grpc_magnetiq_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MagnetiqModeRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MagnetiqModeRef) ProtoMessage() {}

func (x *MagnetiqModeRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_magnetiq_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MagnetiqModeRef.ProtoReflect.Descriptor instead.
func (*MagnetiqModeRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_magnetiq_proto_rawDescGZIP(), []int{3}
}

func (x *MagnetiqModeRef) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *MagnetiqModeRef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

var file_inner_processing_grpc_magnetiq_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*MagnetiqStatusRef)(nil),
		Field:         300001,
		Name:          "processing.magnetiq.magnetiq.magnetiq_status_value",
		Tag:           "bytes,300001,opt,name=magnetiq_status_value",
		Filename:      "inner/processing/grpc/magnetiq.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*MagnetiqStatusRef)(nil),
		Field:         300002,
		Name:          "processing.magnetiq.magnetiq.default_magnetiq_status_value",
		Tag:           "bytes,300002,opt,name=default_magnetiq_status_value",
		Filename:      "inner/processing/grpc/magnetiq.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*MagnetiqFaultErrCodeRef)(nil),
		Field:         300003,
		Name:          "processing.magnetiq.magnetiq.magnetiq_fault_err_code_value",
		Tag:           "bytes,300003,opt,name=magnetiq_fault_err_code_value",
		Filename:      "inner/processing/grpc/magnetiq.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*MagnetiqFaultErrCodeRef)(nil),
		Field:         300004,
		Name:          "processing.magnetiq.magnetiq.default_magnetiq_fault_err_code_value",
		Tag:           "bytes,300004,opt,name=default_magnetiq_fault_err_code_value",
		Filename:      "inner/processing/grpc/magnetiq.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*MagnetiqActionCodeRef)(nil),
		Field:         300005,
		Name:          "processing.magnetiq.magnetiq.magnetiq_action_code_value",
		Tag:           "bytes,300005,opt,name=magnetiq_action_code_value",
		Filename:      "inner/processing/grpc/magnetiq.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*MagnetiqActionCodeRef)(nil),
		Field:         300006,
		Name:          "processing.magnetiq.magnetiq.default_magnetiq_action_code_value",
		Tag:           "bytes,300006,opt,name=default_magnetiq_action_code_value",
		Filename:      "inner/processing/grpc/magnetiq.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*MagnetiqModeRef)(nil),
		Field:         300007,
		Name:          "processing.magnetiq.magnetiq.magnetiq_mode_value",
		Tag:           "bytes,300007,opt,name=magnetiq_mode_value",
		Filename:      "inner/processing/grpc/magnetiq.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*MagnetiqModeRef)(nil),
		Field:         300008,
		Name:          "processing.magnetiq.magnetiq.default_magnetiq_mode_value",
		Tag:           "bytes,300008,opt,name=default_magnetiq_mode_value",
		Filename:      "inner/processing/grpc/magnetiq.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional processing.magnetiq.magnetiq.MagnetiqStatusRef magnetiq_status_value = 300001;
	E_MagnetiqStatusValue = &file_inner_processing_grpc_magnetiq_proto_extTypes[0]
	// optional processing.magnetiq.magnetiq.MagnetiqFaultErrCodeRef magnetiq_fault_err_code_value = 300003;
	E_MagnetiqFaultErrCodeValue = &file_inner_processing_grpc_magnetiq_proto_extTypes[2]
	// optional processing.magnetiq.magnetiq.MagnetiqActionCodeRef magnetiq_action_code_value = 300005;
	E_MagnetiqActionCodeValue = &file_inner_processing_grpc_magnetiq_proto_extTypes[4]
	// optional processing.magnetiq.magnetiq.MagnetiqModeRef magnetiq_mode_value = 300007;
	E_MagnetiqModeValue = &file_inner_processing_grpc_magnetiq_proto_extTypes[6]
)

// Extension fields to descriptorpb.EnumOptions.
var (
	// optional processing.magnetiq.magnetiq.MagnetiqStatusRef default_magnetiq_status_value = 300002;
	E_DefaultMagnetiqStatusValue = &file_inner_processing_grpc_magnetiq_proto_extTypes[1]
	// optional processing.magnetiq.magnetiq.MagnetiqFaultErrCodeRef default_magnetiq_fault_err_code_value = 300004;
	E_DefaultMagnetiqFaultErrCodeValue = &file_inner_processing_grpc_magnetiq_proto_extTypes[3]
	// optional processing.magnetiq.magnetiq.MagnetiqActionCodeRef default_magnetiq_action_code_value = 300006;
	E_DefaultMagnetiqActionCodeValue = &file_inner_processing_grpc_magnetiq_proto_extTypes[5]
	// optional processing.magnetiq.magnetiq.MagnetiqModeRef default_magnetiq_mode_value = 300008;
	E_DefaultMagnetiqModeValue = &file_inner_processing_grpc_magnetiq_proto_extTypes[7]
)

var File_inner_processing_grpc_magnetiq_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_magnetiq_proto_rawDesc = string([]byte{
	0x0a, 0x24, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x2e, 0x6d, 0x61, 0x67, 0x6e,
	0x65, 0x74, 0x69, 0x71, 0x1a, 0x2a, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2e, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x27, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xac, 0x01, 0x0a, 0x11, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x6f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x96, 0x01, 0x0a, 0x17, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71,
	0x46, 0x61, 0x75, 0x6c, 0x74, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x66, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x61, 0x0a, 0x11, 0x69, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x94, 0x01, 0x0a,
	0x15, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x66, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x61, 0x0a, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x22, 0x47, 0x0a, 0x0f, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x4d,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2a, 0xa2, 0x04, 0x0a,
	0x12, 0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x10, 0x00,
	0x1a, 0x19, 0x8a, 0xbe, 0x92, 0x01, 0x0d, 0x12, 0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x18, 0x04, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01, 0x31, 0x12, 0x34, 0x0a, 0x16, 0x4d,
	0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x63,
	0x6c, 0x69, 0x6e, 0x65, 0x64, 0x10, 0x01, 0x1a, 0x18, 0x8a, 0xbe, 0x92, 0x01, 0x0c, 0x12, 0x08,
	0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x64, 0x18, 0x05, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01,
	0x32, 0x12, 0x34, 0x0a, 0x16, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x10, 0x02, 0x1a, 0x18, 0x8a,
	0xbe, 0x92, 0x01, 0x0c, 0x12, 0x08, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x18, 0x0e,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01, 0x33, 0x12, 0x38, 0x0a, 0x18, 0x4d, 0x61, 0x67, 0x6e, 0x65,
	0x74, 0x69, 0x71, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x10, 0x03, 0x1a, 0x1a, 0x8a, 0xbe, 0x92, 0x01, 0x0e, 0x12, 0x0a, 0x55, 0x6e,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x18, 0x05, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01,
	0x34, 0x12, 0x36, 0x0a, 0x17, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x10, 0x04, 0x1a, 0x19,
	0x8a, 0xbe, 0x92, 0x01, 0x0d, 0x12, 0x09, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64,
	0x18, 0x07, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01, 0x35, 0x12, 0x36, 0x0a, 0x17, 0x4d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x65, 0x64, 0x10, 0x05, 0x1a, 0x19, 0x8a, 0xbe, 0x92, 0x01, 0x0d, 0x12, 0x09, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x64, 0x18, 0x09, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01,
	0x36, 0x12, 0x36, 0x0a, 0x17, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x10, 0x06, 0x1a, 0x19,
	0x8a, 0xbe, 0x92, 0x01, 0x0d, 0x12, 0x09, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64,
	0x18, 0x09, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01, 0x37, 0x12, 0x34, 0x0a, 0x16, 0x4d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x76, 0x65, 0x72,
	0x73, 0x65, 0x64, 0x10, 0x07, 0x1a, 0x18, 0x8a, 0xbe, 0x92, 0x01, 0x0c, 0x12, 0x08, 0x52, 0x65,
	0x76, 0x65, 0x72, 0x73, 0x65, 0x64, 0x18, 0x06, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x01, 0x38, 0x1a,
	0x50, 0x92, 0xbe, 0x92, 0x01, 0x0d, 0x12, 0x09, 0x75, 0x6e, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x18, 0x0b, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x1d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec, 0x8e, 0x02, 0x15, 0x6d, 0x61, 0x67, 0x6e,
	0x65, 0x74, 0x69, 0x71, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x2a, 0xc0, 0x31, 0x0a, 0x14, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61,
	0x75, 0x6c, 0x74, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x46, 0x0a, 0x16, 0x4d, 0x61,
	0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x45, 0x72, 0x72, 0x10, 0x00, 0x1a, 0x2a, 0x9a, 0xbe, 0x92, 0x01, 0x19, 0x0a, 0x15, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x20, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x20, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x10, 0x6c, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x31, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x12, 0x42, 0x0a, 0x1b, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61,
	0x75, 0x6c, 0x74, 0x4c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x45, 0x72,
	0x72, 0x10, 0x01, 0x1a, 0x21, 0x9a, 0xbe, 0x92, 0x01, 0x10, 0x0a, 0x0c, 0x4c, 0x6f, 0x63, 0x6b,
	0x20, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x10, 0x6c, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06,
	0x31, 0x30, 0x30, 0x30, 0x30, 0x31, 0x12, 0x49, 0x0a, 0x1e, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74,
	0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4e, 0x6f, 0x74,
	0x46, 0x6f, 0x75, 0x6e, 0x64, 0x45, 0x72, 0x72, 0x10, 0x02, 0x1a, 0x25, 0x9a, 0xbe, 0x92, 0x01,
	0x14, 0x0a, 0x10, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x66, 0x6f,
	0x75, 0x6e, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x32, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x12, 0x40, 0x0a, 0x1a, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75,
	0x6c, 0x74, 0x42, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x72, 0x72, 0x10,
	0x03, 0x1a, 0x20, 0x9a, 0xbe, 0x92, 0x01, 0x0f, 0x0a, 0x0b, 0x42, 0x61, 0x64, 0x20, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x33, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x12, 0x50, 0x0a, 0x22, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46,
	0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x69, 0x74, 0x79, 0x56, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x10, 0x04, 0x1a, 0x28, 0x9a, 0xbe, 0x92,
	0x01, 0x17, 0x0a, 0x13, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x69, 0x74, 0x79, 0x20, 0x76, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x34,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x12, 0x55, 0x0a, 0x23, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x4d, 0x75, 0x73,
	0x74, 0x42, 0x65, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x45, 0x72, 0x72, 0x10, 0x05, 0x1a, 0x2c,
	0x9a, 0xbe, 0x92, 0x01, 0x1b, 0x0a, 0x17, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x20, 0x49, 0x44, 0x20,
	0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x10, 0x6a,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x34, 0x30, 0x30, 0x30, 0x31, 0x31, 0x12, 0x5c, 0x0a, 0x26,
	0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x06, 0x1a, 0x30, 0x9a, 0xbe, 0x92, 0x01, 0x1f, 0x0a,
	0x1b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x20, 0x69, 0x73, 0x20, 0x61, 0x6c, 0x72, 0x65, 0x61, 0x64,
	0x79, 0x20, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x06, 0x34, 0x30, 0x30, 0x30, 0x31, 0x34, 0x12, 0x47, 0x0a, 0x1d, 0x4d, 0x61,
	0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x45, 0x72, 0x72, 0x10, 0x07, 0x1a, 0x24, 0x9a,
	0xbe, 0x92, 0x01, 0x13, 0x0a, 0x0f, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x20, 0x6e, 0x6f, 0x74, 0x20,
	0x66, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x32, 0x30, 0x30,
	0x32, 0x30, 0x31, 0x12, 0x5e, 0x0a, 0x28, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46,
	0x61, 0x75, 0x6c, 0x74, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x45, 0x72, 0x72, 0x10,
	0x08, 0x1a, 0x30, 0x9a, 0xbe, 0x92, 0x01, 0x1f, 0x0a, 0x1b, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x20, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x20, 0x6e, 0x6f, 0x74, 0x20,
	0x66, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x32, 0x30, 0x30,
	0x33, 0x30, 0x31, 0x12, 0x70, 0x0a, 0x1b, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46,
	0x61, 0x75, 0x6c, 0x74, 0x4b, 0x65, 0x79, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x45,
	0x72, 0x72, 0x10, 0x09, 0x1a, 0x4f, 0x9a, 0xbe, 0x92, 0x01, 0x3e, 0x0a, 0x3a, 0x4b, 0x65, 0x79,
	0x20, 0x6e, 0x6f, 0x74, 0x20, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x2e, 0x20, 0x45, 0x69, 0x74, 0x68,
	0x65, 0x72, 0x20, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x46, 0x41, 0x43, 0x45, 0x20, 0x6f, 0x72, 0x20,
	0x4b, 0x45, 0x59, 0x5f, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x20, 0x69, 0x73, 0x20, 0x69, 0x6e, 0x63,
	0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x2e, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x32,
	0x30, 0x30, 0x34, 0x30, 0x31, 0x12, 0x4f, 0x0a, 0x21, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x4e,
	0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x45, 0x72, 0x72, 0x10, 0x0a, 0x1a, 0x28, 0x9a, 0xbe,
	0x92, 0x01, 0x17, 0x0a, 0x13, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x20, 0x6e,
	0x6f, 0x74, 0x20, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06,
	0x32, 0x30, 0x30, 0x36, 0x30, 0x31, 0x12, 0x73, 0x0a, 0x29, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74,
	0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x72, 0x72, 0x10, 0x0b, 0x1a, 0x44, 0x9a, 0xbe, 0x92, 0x01, 0x33, 0x0a, 0x2f, 0x43, 0x6f,
	0x75, 0x6c, 0x64, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x20, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2c, 0x20, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20,
	0x66, 0x6c, 0x6f, 0x77, 0x20, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x10, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x06, 0x35, 0x30, 0x35, 0x31, 0x30, 0x32, 0x12, 0x70, 0x0a, 0x20, 0x4d,
	0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x41, 0x75, 0x74, 0x68,
	0x4e, 0x6f, 0x6e, 0x33, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x72, 0x72, 0x10,
	0x0c, 0x1a, 0x4a, 0x9a, 0xbe, 0x92, 0x01, 0x39, 0x0a, 0x35, 0x43, 0x6f, 0x75, 0x6c, 0x64, 0x20,
	0x6e, 0x6f, 0x74, 0x20, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x20, 0x61,
	0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x6f, 0x66,
	0x20, 0x6e, 0x6f, 0x6e, 0x2d, 0x33, 0x44, 0x20, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x10,
	0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x36, 0x30, 0x35, 0x31, 0x30, 0x33, 0x12, 0x71, 0x0a,
	0x23, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x41, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x45, 0x72, 0x72, 0x10, 0x0d, 0x1a, 0x48, 0x9a, 0xbe, 0x92, 0x01, 0x37, 0x0a, 0x33, 0x43,
	0x6f, 0x75, 0x6c, 0x64, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x20, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2c, 0x20, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x20, 0x66, 0x6c, 0x6f, 0x77, 0x20, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x35, 0x30, 0x35, 0x31, 0x30, 0x34,
	0x12, 0x68, 0x0a, 0x28, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c,
	0x74, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x6e,
	0x33, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x72, 0x72, 0x10, 0x0e, 0x1a, 0x3a,
	0x9a, 0xbe, 0x92, 0x01, 0x29, 0x0a, 0x25, 0x43, 0x6f, 0x75, 0x6c, 0x64, 0x20, 0x6e, 0x6f, 0x74,
	0x20, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x20, 0x6e, 0x6f,
	0x6e, 0x2d, 0x33, 0x44, 0x20, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x06, 0x36, 0x30, 0x35, 0x31, 0x30, 0x35, 0x12, 0x67, 0x0a, 0x1e, 0x4d, 0x61,
	0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x72, 0x72, 0x10, 0x0f, 0x1a, 0x43,
	0x9a, 0xbe, 0x92, 0x01, 0x32, 0x0a, 0x2e, 0x43, 0x6f, 0x75, 0x6c, 0x64, 0x20, 0x6e, 0x6f, 0x74,
	0x20, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x20, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2c, 0x20, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x66, 0x6c, 0x6f, 0x77, 0x20, 0x76, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x35, 0x30, 0x35,
	0x31, 0x30, 0x36, 0x12, 0x65, 0x0a, 0x1d, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46,
	0x61, 0x75, 0x6c, 0x74, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x45, 0x72, 0x72, 0x10, 0x10, 0x1a, 0x42, 0x9a, 0xbe, 0x92, 0x01, 0x31, 0x0a, 0x2d, 0x43,
	0x6f, 0x75, 0x6c, 0x64, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x20,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2c, 0x20, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x66,
	0x6c, 0x6f, 0x77, 0x20, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x06, 0x35, 0x30, 0x35, 0x31, 0x30, 0x37, 0x12, 0x50, 0x0a, 0x20, 0x4d, 0x61,
	0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x11,
	0x1a, 0x2a, 0x9a, 0xbe, 0x92, 0x01, 0x19, 0x0a, 0x15, 0x43, 0x61, 0x72, 0x64, 0x20, 0x6e, 0x61,
	0x6d, 0x65, 0x20, 0x69, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x30, 0x35, 0x31, 0x30, 0x39, 0x12, 0x54, 0x0a, 0x22,
	0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45,
	0x72, 0x72, 0x10, 0x12, 0x1a, 0x2c, 0x9a, 0xbe, 0x92, 0x01, 0x1b, 0x0a, 0x17, 0x43, 0x61, 0x72,
	0x64, 0x20, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x20, 0x69, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x30, 0x35, 0x31,
	0x31, 0x30, 0x12, 0x54, 0x0a, 0x22, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61,
	0x75, 0x6c, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x13, 0x1a, 0x2c, 0x9a, 0xbe, 0x92, 0x01,
	0x1b, 0x0a, 0x17, 0x43, 0x61, 0x72, 0x64, 0x20, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x20, 0x69,
	0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x06, 0x37, 0x30, 0x35, 0x31, 0x31, 0x31, 0x12, 0x58, 0x0a, 0x25, 0x4d, 0x61, 0x67, 0x6e,
	0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x43, 0x61, 0x72, 0x64, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x45, 0x72,
	0x72, 0x10, 0x14, 0x1a, 0x2d, 0x9a, 0xbe, 0x92, 0x01, 0x1c, 0x0a, 0x18, 0x49, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x20, 0x63, 0x61, 0x72, 0x64, 0x20, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x20,
	0x64, 0x61, 0x74, 0x65, 0x10, 0x75, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x33, 0x30, 0x35, 0x31,
	0x31, 0x32, 0x12, 0x4f, 0x0a, 0x21, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61,
	0x75, 0x6c, 0x74, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x45, 0x72, 0x72, 0x10, 0x15, 0x1a, 0x28, 0x9a, 0xbe, 0x92, 0x01, 0x17,
	0x0a, 0x13, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x63, 0x61, 0x72, 0x64, 0x20, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x10, 0x74, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x33, 0x30, 0x35,
	0x31, 0x31, 0x33, 0x12, 0x6f, 0x0a, 0x26, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46,
	0x61, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x75, 0x6c, 0x64, 0x4e, 0x6f, 0x74, 0x52, 0x65, 0x76, 0x65,
	0x72, 0x73, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x72, 0x72, 0x10, 0x16, 0x1a,
	0x43, 0x9a, 0xbe, 0x92, 0x01, 0x32, 0x0a, 0x2e, 0x43, 0x6f, 0x75, 0x6c, 0x64, 0x20, 0x6e, 0x6f,
	0x74, 0x20, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x20, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2c, 0x20, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x66, 0x6c, 0x6f, 0x77, 0x20, 0x76, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x35, 0x30,
	0x35, 0x31, 0x31, 0x35, 0x12, 0x5e, 0x0a, 0x27, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71,
	0x46, 0x61, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10,
	0x17, 0x1a, 0x31, 0x9a, 0xbe, 0x92, 0x01, 0x20, 0x0a, 0x1c, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x20, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x20, 0x69, 0x73, 0x20,
	0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x35, 0x30,
	0x35, 0x31, 0x31, 0x36, 0x12, 0x4a, 0x0a, 0x1f, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71,
	0x46, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x45, 0x72, 0x72, 0x10, 0x18, 0x1a, 0x25, 0x9a, 0xbe, 0x92, 0x01, 0x14,
	0x0a, 0x10, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x36, 0x30, 0x35, 0x31, 0x31, 0x37,
	0x12, 0x7f, 0x0a, 0x28, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c,
	0x74, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4e,
	0x6f, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x19, 0x1a, 0x51,
	0x9a, 0xbe, 0x92, 0x01, 0x40, 0x0a, 0x3c, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x20,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x20, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x20, 0x61, 0x72, 0x65, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x20, 0x6f, 0x6e, 0x20, 0x74, 0x68, 0x69, 0x73, 0x20, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x10, 0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x36, 0x30, 0x35, 0x31, 0x31,
	0x38, 0x12, 0x45, 0x0a, 0x1b, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75,
	0x6c, 0x74, 0x43, 0x53, 0x43, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72,
	0x10, 0x1a, 0x1a, 0x24, 0x9a, 0xbe, 0x92, 0x01, 0x13, 0x0a, 0x0f, 0x43, 0x53, 0x43, 0x20, 0x69,
	0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x06, 0x37, 0x30, 0x35, 0x31, 0x32, 0x30, 0x12, 0x43, 0x0a, 0x1a, 0x4d, 0x61, 0x67, 0x6e,
	0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x53, 0x43, 0x49, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x45, 0x72, 0x72, 0x10, 0x1b, 0x1a, 0x23, 0x9a, 0xbe, 0x92, 0x01, 0x12, 0x0a,
	0x0e, 0x43, 0x53, 0x43, 0x20, 0x69, 0x73, 0x20, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10,
	0x73, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x33, 0x30, 0x35, 0x31, 0x32, 0x32, 0x12, 0x56, 0x0a,
	0x23, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x45, 0x72, 0x72, 0x10, 0x1c, 0x1a, 0x2d, 0x9a, 0xbe, 0x92, 0x01, 0x1c, 0x0a, 0x18, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x20, 0x69, 0x73, 0x20, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37,
	0x31, 0x30, 0x31, 0x30, 0x31, 0x12, 0x58, 0x0a, 0x24, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x1d, 0x1a,
	0x2e, 0x9a, 0xbe, 0x92, 0x01, 0x1d, 0x0a, 0x19, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x20, 0x69, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x31, 0x30, 0x31, 0x30, 0x32, 0x12,
	0x5d, 0x0a, 0x26, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x1e, 0x1a, 0x31, 0x9a, 0xbe, 0x92,
	0x01, 0x20, 0x0a, 0x1c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x49, 0x44, 0x20, 0x69, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x31, 0x30, 0x31, 0x30, 0x33, 0x12, 0x65,
	0x0a, 0x2a, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x1f, 0x1a, 0x35,
	0x9a, 0xbe, 0x92, 0x01, 0x24, 0x0a, 0x20, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x69, 0x73, 0x20, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37,
	0x31, 0x30, 0x31, 0x30, 0x34, 0x12, 0x69, 0x0a, 0x2c, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x20, 0x1a, 0x37, 0x9a, 0xbe, 0x92, 0x01, 0x26, 0x0a, 0x22,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x20, 0x69, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x31, 0x30, 0x31, 0x30, 0x35,
	0x12, 0x6f, 0x0a, 0x2f, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c,
	0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x45, 0x72, 0x72, 0x10, 0x21, 0x1a, 0x3a, 0x9a, 0xbe, 0x92, 0x01, 0x29, 0x0a, 0x25, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x31, 0x30, 0x31, 0x30,
	0x36, 0x12, 0x58, 0x0a, 0x24, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75,
	0x6c, 0x74, 0x44, 0x33, 0x44, 0x41, 0x43, 0x53, 0x50, 0x61, 0x72, 0x52, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x22, 0x1a, 0x2e, 0x9a, 0xbe, 0x92,
	0x01, 0x1d, 0x0a, 0x19, 0x44, 0x33, 0x44, 0x2e, 0x41, 0x43, 0x53, 0x2e, 0x50, 0x61, 0x52, 0x65,
	0x73, 0x20, 0x69, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x31, 0x30, 0x31, 0x30, 0x39, 0x12, 0x4c, 0x0a, 0x20, 0x4d,
	0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x63, 0x75,
	0x72, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10,
	0x23, 0x1a, 0x26, 0x9a, 0xbe, 0x92, 0x01, 0x15, 0x0a, 0x11, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x20, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x06, 0x35, 0x31, 0x30, 0x31, 0x31, 0x34, 0x12, 0x55, 0x0a, 0x24, 0x4d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x54, 0x6f, 0x6f, 0x46, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x72,
	0x72, 0x10, 0x24, 0x1a, 0x2b, 0x9a, 0xbe, 0x92, 0x01, 0x1a, 0x0a, 0x16, 0x54, 0x6f, 0x6f, 0x20,
	0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x74, 0x20, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x10, 0x68, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x35, 0x31, 0x30, 0x31, 0x31, 0x35,
	0x12, 0x5d, 0x0a, 0x28, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c,
	0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x25, 0x1a, 0x2f,
	0x9a, 0xbe, 0x92, 0x01, 0x1e, 0x0a, 0x1a, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x31, 0x30, 0x31, 0x31, 0x36, 0x12,
	0x61, 0x0a, 0x2a, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74,
	0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x26, 0x1a,
	0x31, 0x9a, 0xbe, 0x92, 0x01, 0x20, 0x0a, 0x1c, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x20, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x31, 0x30, 0x31,
	0x31, 0x37, 0x12, 0x62, 0x0a, 0x29, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61,
	0x75, 0x6c, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x45, 0x72, 0x72, 0x10,
	0x27, 0x1a, 0x33, 0x9a, 0xbe, 0x92, 0x01, 0x22, 0x0a, 0x1e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x20, 0x69, 0x73, 0x20, 0x69,
	0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06,
	0x36, 0x31, 0x30, 0x31, 0x31, 0x38, 0x12, 0x64, 0x0a, 0x26, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74,
	0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x46, 0x75, 0x74, 0x75, 0x72, 0x65, 0x45, 0x72, 0x72,
	0x10, 0x28, 0x1a, 0x38, 0x9a, 0xbe, 0x92, 0x01, 0x27, 0x0a, 0x23, 0x52, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x20, 0x6d, 0x75, 0x73,
	0x74, 0x20, 0x62, 0x65, 0x20, 0x69, 0x6e, 0x20, 0x66, 0x75, 0x74, 0x75, 0x72, 0x65, 0x10, 0x6a,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x35, 0x31, 0x30, 0x31, 0x31, 0x39, 0x12, 0x76, 0x0a, 0x31,
	0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x47,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x54, 0x68, 0x61, 0x6e, 0x5a, 0x65, 0x72, 0x6f, 0x45, 0x72,
	0x72, 0x10, 0x29, 0x1a, 0x3f, 0x9a, 0xbe, 0x92, 0x01, 0x2e, 0x0a, 0x2a, 0x52, 0x65, 0x63, 0x75,
	0x72, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x20,
	0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x67, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x20,
	0x74, 0x68, 0x61, 0x6e, 0x20, 0x30, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x35, 0x31,
	0x30, 0x31, 0x32, 0x30, 0x12, 0x56, 0x0a, 0x23, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71,
	0x46, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x44,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x2a, 0x1a, 0x2d, 0x9a,
	0xbe, 0x92, 0x01, 0x1c, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x49, 0x44, 0x20, 0x69, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x31, 0x30, 0x31, 0x32, 0x31, 0x12, 0x59, 0x0a, 0x25,
	0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x2b, 0x1a, 0x2e, 0x9a, 0xbe, 0x92, 0x01, 0x1d, 0x0a, 0x19,
	0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x20, 0x69, 0x73,
	0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x06, 0x37, 0x31, 0x30, 0x31, 0x32, 0x32, 0x12, 0x4c, 0x0a, 0x20, 0x4d, 0x61, 0x67, 0x6e, 0x65,
	0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x53,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x72, 0x72, 0x10, 0x2c, 0x1a, 0x26, 0x9a,
	0xbe, 0x92, 0x01, 0x15, 0x0a, 0x11, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x31,
	0x31, 0x30, 0x32, 0x30, 0x31, 0x12, 0x4d, 0x0a, 0x1f, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x2d, 0x1a, 0x28, 0x9a, 0xbe, 0x92, 0x01,
	0x17, 0x0a, 0x13, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x20, 0x69, 0x73, 0x20, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x31,
	0x30, 0x32, 0x30, 0x33, 0x12, 0x51, 0x0a, 0x21, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71,
	0x46, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x2e, 0x1a, 0x2a, 0x9a, 0xbe, 0x92,
	0x01, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x46, 0x41, 0x43, 0x45, 0x20, 0x69, 0x73,
	0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x06, 0x37, 0x31, 0x30, 0x32, 0x30, 0x34, 0x12, 0x4f, 0x0a, 0x20, 0x4d, 0x61, 0x67, 0x6e, 0x65,
	0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x4b, 0x65, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x2f, 0x1a, 0x29, 0x9a,
	0xbe, 0x92, 0x01, 0x18, 0x0a, 0x14, 0x4b, 0x45, 0x59, 0x49, 0x4e, 0x44, 0x45, 0x58, 0x20, 0x69,
	0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x06, 0x37, 0x31, 0x30, 0x32, 0x30, 0x35, 0x12, 0x45, 0x0a, 0x1b, 0x4d, 0x61, 0x67, 0x6e,
	0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x30, 0x1a, 0x24, 0x9a, 0xbe, 0x92, 0x01, 0x13,
	0x0a, 0x0f, 0x4b, 0x45, 0x59, 0x20, 0x69, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x31, 0x30, 0x32, 0x30, 0x36, 0x12,
	0x47, 0x0a, 0x1c, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10,
	0x31, 0x1a, 0x25, 0x9a, 0xbe, 0x92, 0x01, 0x14, 0x0a, 0x10, 0x44, 0x41, 0x54, 0x41, 0x20, 0x69,
	0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x06, 0x37, 0x31, 0x30, 0x32, 0x30, 0x37, 0x12, 0x51, 0x0a, 0x21, 0x4d, 0x61, 0x67, 0x6e,
	0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x32, 0x1a,
	0x2a, 0x9a, 0xbe, 0x92, 0x01, 0x19, 0x0a, 0x15, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x20, 0x69, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x31, 0x30, 0x32, 0x30, 0x38, 0x12, 0x5d, 0x0a, 0x28, 0x4d,
	0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x58, 0x4d, 0x4c, 0x44,
	0x65, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x33, 0x1a, 0x2f, 0x9a, 0xbe, 0x92, 0x01, 0x1e,
	0x0a, 0x1a, 0x58, 0x4d, 0x4c, 0x20, 0x64, 0x65, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x06, 0x31, 0x31, 0x30, 0x32, 0x30, 0x39, 0x12, 0x59, 0x0a, 0x26, 0x4d, 0x61,
	0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x58, 0x4d, 0x4c, 0x53, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x45, 0x72, 0x72, 0x10, 0x34, 0x1a, 0x2d, 0x9a, 0xbe, 0x92, 0x01, 0x1c, 0x0a, 0x18, 0x58,
	0x4d, 0x4c, 0x20, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x20, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x31,
	0x31, 0x30, 0x32, 0x31, 0x30, 0x12, 0x55, 0x0a, 0x24, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x61, 0x74, 0x61, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x35, 0x1a,
	0x2b, 0x9a, 0xbe, 0x92, 0x01, 0x1a, 0x0a, 0x16, 0x44, 0x61, 0x74, 0x61, 0x20, 0x64, 0x65, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x6a,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x31, 0x31, 0x30, 0x33, 0x30, 0x31, 0x12, 0x55, 0x0a, 0x24,
	0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x45, 0x72, 0x72, 0x10, 0x36, 0x1a, 0x2b, 0x9a, 0xbe, 0x92, 0x01, 0x1a, 0x0a, 0x16, 0x44,
	0x61, 0x74, 0x61, 0x20, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x66,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x31, 0x31, 0x30,
	0x33, 0x30, 0x32, 0x12, 0x63, 0x0a, 0x2b, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46,
	0x61, 0x75, 0x6c, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x45,
	0x72, 0x72, 0x10, 0x37, 0x1a, 0x32, 0x9a, 0xbe, 0x92, 0x01, 0x21, 0x0a, 0x1d, 0x53, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x20, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x20, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x06, 0x31, 0x31, 0x30, 0x34, 0x30, 0x31, 0x12, 0x46, 0x0a, 0x1d, 0x4d, 0x61, 0x67, 0x6e,
	0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67,
	0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x45, 0x72, 0x72, 0x10, 0x38, 0x1a, 0x23, 0x9a, 0xbe, 0x92,
	0x01, 0x12, 0x0a, 0x0e, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67, 0x20, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x31, 0x31, 0x30, 0x34, 0x30, 0x32,
	0x12, 0x4b, 0x0a, 0x1f, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c,
	0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64,
	0x45, 0x72, 0x72, 0x10, 0x39, 0x1a, 0x26, 0x9a, 0xbe, 0x92, 0x01, 0x15, 0x0a, 0x11, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x10,
	0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x32, 0x31, 0x30, 0x35, 0x30, 0x31, 0x12, 0x7f, 0x0a,
	0x30, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72,
	0x72, 0x10, 0x3a, 0x1a, 0x49, 0x9a, 0xbe, 0x92, 0x01, 0x38, 0x0a, 0x34, 0x43, 0x6f, 0x75, 0x6c,
	0x64, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61,
	0x74, 0x65, 0x20, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2c, 0x20, 0x69, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x20, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x36, 0x31, 0x30, 0x36, 0x30, 0x32, 0x12, 0x4d,
	0x0a, 0x1f, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x72,
	0x72, 0x10, 0x3b, 0x1a, 0x28, 0x9a, 0xbe, 0x92, 0x01, 0x17, 0x0a, 0x13, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x20, 0x69, 0x73, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10,
	0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x37, 0x31, 0x30, 0x37, 0x30, 0x31, 0x12, 0x73, 0x0a,
	0x2a, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x10, 0x3c, 0x1a, 0x43, 0x9a,
	0xbe, 0x92, 0x01, 0x32, 0x0a, 0x2e, 0x43, 0x6f, 0x75, 0x6c, 0x64, 0x20, 0x6e, 0x6f, 0x74, 0x20,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x20, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2c, 0x20,
	0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x4d, 0x6f, 0x64, 0x65, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x36, 0x31, 0x30, 0x37,
	0x30, 0x32, 0x12, 0x4c, 0x0a, 0x1e, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61,
	0x75, 0x6c, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x45, 0x72, 0x72, 0x10, 0x3d, 0x1a, 0x28, 0x9a, 0xbe, 0x92, 0x01, 0x17, 0x0a, 0x13, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x20, 0x61, 0x72, 0x65, 0x20, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x31, 0x31, 0x31, 0x31, 0x30, 0x31,
	0x12, 0x5b, 0x0a, 0x20, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c,
	0x74, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x45, 0x72, 0x72, 0x10, 0x3e, 0x1a, 0x35, 0x9a, 0xbe, 0x92, 0x01, 0x24, 0x0a, 0x20, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x20, 0x68, 0x6f, 0x6c, 0x64, 0x73, 0x20, 0x69, 0x6e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x20, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x20, 0x74, 0x79, 0x70, 0x65, 0x10,
	0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x32, 0x30, 0x30, 0x32, 0x30, 0x33, 0x12, 0x6c, 0x0a,
	0x22, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x55, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x45, 0x72, 0x72, 0x10, 0x3f, 0x1a, 0x44, 0x9a, 0xbe, 0x92, 0x01, 0x33, 0x0a, 0x2f, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x20, 0x77, 0x61, 0x73, 0x20, 0x75, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x20,
	0x74, 0x6f, 0x20, 0x64, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x20, 0x74, 0x68, 0x65, 0x20, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x20, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x10, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x06, 0x32, 0x30, 0x30, 0x32, 0x30, 0x34, 0x12, 0x53, 0x0a, 0x1f, 0x4d,
	0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x63, 0x6f,
	0x72, 0x72, 0x65, 0x63, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x72, 0x72, 0x10, 0x40,
	0x1a, 0x2e, 0x9a, 0xbe, 0x92, 0x01, 0x1d, 0x0a, 0x19, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x20, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x69, 0x73, 0x20, 0x69, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65,
	0x63, 0x74, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x32, 0x30, 0x30, 0x32, 0x30, 0x35,
	0x12, 0x57, 0x0a, 0x21, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c,
	0x74, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x45, 0x72, 0x72, 0x10, 0x41, 0x1a, 0x30, 0x9a, 0xbe, 0x92, 0x01, 0x1f, 0x0a, 0x1b,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x20, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x20, 0x69,
	0x73, 0x20, 0x69, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x10, 0x6a, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x06, 0x32, 0x30, 0x30, 0x32, 0x30, 0x36, 0x12, 0x5b, 0x0a, 0x23, 0x4d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x63, 0x74, 0x43, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x45, 0x72, 0x72,
	0x10, 0x42, 0x1a, 0x32, 0x9a, 0xbe, 0x92, 0x01, 0x21, 0x0a, 0x1d, 0x43, 0x72, 0x79, 0x70, 0x74,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x20, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x20, 0x69, 0x73, 0x20, 0x69,
	0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06,
	0x32, 0x30, 0x30, 0x32, 0x30, 0x37, 0x12, 0x5a, 0x0a, 0x22, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74,
	0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x10, 0x43, 0x1a, 0x32,
	0x9a, 0xbe, 0x92, 0x01, 0x21, 0x0a, 0x1d, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x20, 0x6d, 0x6f, 0x64,
	0x65, 0x20, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x20, 0x69, 0x73, 0x20, 0x69, 0x6e, 0x63, 0x6f, 0x72,
	0x72, 0x65, 0x63, 0x74, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x36, 0x30, 0x35, 0x31,
	0x30, 0x36, 0x1a, 0x60, 0xa2, 0xbe, 0x92, 0x01, 0x0d, 0x0a, 0x09, 0x75, 0x6e, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x10, 0x64, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x25, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x5f, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0xb2, 0x82, 0xec, 0x8e, 0x02, 0x1d, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x5f,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x2a, 0xe7, 0x34, 0x0a, 0x16, 0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x38, 0x0a, 0x1a, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x10, 0x00, 0x1a,
	0x18, 0xaa, 0xbe, 0x92, 0x01, 0x0a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x30, 0x30, 0x30, 0x12, 0x56, 0x0a, 0x20, 0x4d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44,
	0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x10, 0x01, 0x1a,
	0x30, 0xaa, 0xbe, 0x92, 0x01, 0x22, 0x0a, 0x1e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x20,
	0x28, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x2c, 0x20, 0x6e, 0x6f, 0x20, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x29, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x30,
	0x30, 0x12, 0x51, 0x0a, 0x24, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x10, 0x02, 0x1a, 0x27, 0xaa, 0xbe, 0x92,
	0x01, 0x19, 0x0a, 0x15, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x64, 0x20, 0x63, 0x61, 0x72, 0x64, 0x10, 0x72, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x03, 0x31, 0x30, 0x31, 0x12, 0x57, 0x0a, 0x27, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e,
	0x65, 0x53, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46, 0x72, 0x61, 0x75, 0x64, 0x10,
	0x03, 0x1a, 0x2a, 0xaa, 0xbe, 0x92, 0x01, 0x1c, 0x0a, 0x18, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e,
	0x65, 0x2c, 0x20, 0x73, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x20, 0x66, 0x72, 0x61,
	0x75, 0x64, 0x10, 0x77, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x30, 0x32, 0x12, 0x67, 0x0a,
	0x28, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x10, 0x04, 0x1a, 0x39, 0xaa, 0xbe, 0x92,
	0x01, 0x2b, 0x0a, 0x27, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x63, 0x61, 0x72,
	0x64, 0x20, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x20, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x20, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x10, 0x6a, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x03, 0x31, 0x30, 0x33, 0x12, 0x57, 0x0a, 0x27, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74,
	0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x65, 0x64, 0x43, 0x61, 0x72,
	0x64, 0x10, 0x05, 0x1a, 0x2a, 0xaa, 0xbe, 0x92, 0x01, 0x1c, 0x0a, 0x18, 0x44, 0x65, 0x63, 0x6c,
	0x69, 0x6e, 0x65, 0x2c, 0x20, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x65, 0x64, 0x20,
	0x63, 0x61, 0x72, 0x64, 0x10, 0x66, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x30, 0x34, 0x12,
	0x7b, 0x0a, 0x29, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x61, 0x6c, 0x6c,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x70, 0x74, 0x10, 0x06, 0x1a, 0x4c,
	0xaa, 0xbe, 0x92, 0x01, 0x3e, 0x0a, 0x3a, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20,
	0x63, 0x61, 0x72, 0x64, 0x20, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x20, 0x63, 0x61,
	0x6c, 0x6c, 0x20, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x27, 0x73, 0x20, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x20, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x30, 0x35, 0x12, 0x5b, 0x0a, 0x26,
	0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x54, 0x6f,
	0x49, 0x73, 0x73, 0x75, 0x65, 0x72, 0x10, 0x07, 0x1a, 0x2f, 0xaa, 0xbe, 0x92, 0x01, 0x21, 0x0a,
	0x1d, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x72, 0x65, 0x66, 0x65, 0x72, 0x20,
	0x74, 0x6f, 0x20, 0x63, 0x61, 0x72, 0x64, 0x20, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x10, 0x69,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x30, 0x37, 0x12, 0x7a, 0x0a, 0x30, 0x4d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44,
	0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x73, 0x73, 0x75, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x10, 0x08, 0x1a,
	0x44, 0xaa, 0xbe, 0x92, 0x01, 0x36, 0x0a, 0x32, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c,
	0x20, 0x72, 0x65, 0x66, 0x65, 0x72, 0x20, 0x74, 0x6f, 0x20, 0x63, 0x61, 0x72, 0x64, 0x20, 0x69,
	0x73, 0x73, 0x75, 0x65, 0x72, 0x27, 0x73, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x20,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x10, 0x69, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x03, 0x31, 0x30, 0x38, 0x12, 0x59, 0x0a, 0x28, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69,
	0x6e, 0x65, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x10, 0x09, 0x1a, 0x2b, 0xaa, 0xbe, 0x92, 0x01, 0x1d, 0x0a, 0x19, 0x44, 0x65, 0x63, 0x6c,
	0x69, 0x6e, 0x65, 0x2c, 0x20, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x10, 0x6d, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x30, 0x39,
	0x12, 0x55, 0x0a, 0x26, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x6e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0x0a, 0x1a, 0x29, 0xaa, 0xbe,
	0x92, 0x01, 0x1b, 0x0a, 0x17, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x69, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x03, 0x31, 0x31, 0x30, 0x12, 0x5e, 0x0a, 0x2a, 0x4d, 0x61, 0x67, 0x6e, 0x65,
	0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63,
	0x6c, 0x69, 0x6e, 0x65, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x61, 0x72, 0x64, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x10, 0x0b, 0x1a, 0x2e, 0xaa, 0xbe, 0x92, 0x01, 0x20, 0x0a, 0x1c,
	0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x20, 0x63, 0x61, 0x72, 0x64, 0x20, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x10, 0x74, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x03, 0x31, 0x31, 0x31, 0x12, 0x59, 0x0a, 0x28, 0x4d, 0x61, 0x67, 0x6e, 0x65,
	0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63,
	0x6c, 0x69, 0x6e, 0x65, 0x55, 0x6e, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x46, 0x65, 0x65, 0x10, 0x0c, 0x1a, 0x2b, 0xaa, 0xbe, 0x92, 0x01, 0x1d, 0x0a, 0x19, 0x44, 0x65,
	0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x75, 0x6e, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x20, 0x66, 0x65, 0x65, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31,
	0x31, 0x33, 0x12, 0x63, 0x0a, 0x26, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4e,
	0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x10, 0x0d, 0x1a, 0x37,
	0xaa, 0xbe, 0x92, 0x01, 0x29, 0x0a, 0x25, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20,
	0x6e, 0x6f, 0x20, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x74, 0x79,
	0x70, 0x65, 0x20, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x03, 0x31, 0x31, 0x34, 0x12, 0x6e, 0x0a, 0x2d, 0x4d, 0x61, 0x67, 0x6e, 0x65,
	0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63,
	0x6c, 0x69, 0x6e, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x74, 0x53,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x10, 0x0e, 0x1a, 0x3b, 0xaa, 0xbe, 0x92, 0x01,
	0x2d, 0x0a, 0x29, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x20, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x6e,
	0x6f, 0x74, 0x20, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x03, 0x31, 0x31, 0x35, 0x12, 0x5f, 0x0a, 0x2a, 0x4d, 0x61, 0x67, 0x6e, 0x65,
	0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63,
	0x6c, 0x69, 0x6e, 0x65, 0x49, 0x6e, 0x73, 0x75, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x74,
	0x46, 0x75, 0x6e, 0x64, 0x73, 0x10, 0x0f, 0x1a, 0x2f, 0xaa, 0xbe, 0x92, 0x01, 0x21, 0x0a, 0x1d,
	0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x73, 0x75, 0x66,
	0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x74, 0x20, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x10, 0x76, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x31, 0x36, 0x12, 0x54, 0x0a, 0x25, 0x4d, 0x61, 0x67, 0x6e,
	0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65,
	0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x6f, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x10, 0x10, 0x1a, 0x29, 0xaa, 0xbe, 0x92, 0x01, 0x1b, 0x0a, 0x17, 0x44, 0x65, 0x63, 0x6c,
	0x69, 0x6e, 0x65, 0x2c, 0x20, 0x6e, 0x6f, 0x20, 0x63, 0x61, 0x72, 0x64, 0x20, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x31, 0x38, 0x12, 0x79,
	0x0a, 0x31, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x6f, 0x74, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x54, 0x6f, 0x43, 0x61, 0x72, 0x64, 0x68, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x10, 0x11, 0x1a, 0x42, 0xaa, 0xbe, 0x92, 0x01, 0x34, 0x0a, 0x30, 0x44, 0x65,
	0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64,
	0x20, 0x74, 0x6f, 0x20, 0x63, 0x61, 0x72, 0x64, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x10, 0x6a,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x31, 0x39, 0x12, 0x75, 0x0a, 0x2f, 0x4d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44,
	0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x6f, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x74, 0x74,
	0x65, 0x64, 0x54, 0x6f, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x10, 0x12, 0x1a, 0x40,
	0xaa, 0xbe, 0x92, 0x01, 0x32, 0x0a, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x6e, 0x6f, 0x74, 0x20,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x20, 0x74, 0x6f, 0x20, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x32, 0x30,
	0x12, 0x6f, 0x0a, 0x2f, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x63,
	0x65, 0x65, 0x64, 0x73, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x10, 0x13, 0x1a, 0x3a, 0xaa, 0xbe, 0x92, 0x01, 0x2c, 0x0a, 0x28, 0x44, 0x65,
	0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x65, 0x78, 0x63, 0x65, 0x65, 0x64, 0x73, 0x20, 0x77,
	0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x20, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x20, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x67, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x32,
	0x31, 0x12, 0x5d, 0x0a, 0x2a, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10,
	0x14, 0x1a, 0x2d, 0xaa, 0xbe, 0x92, 0x01, 0x1f, 0x0a, 0x1b, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e,
	0x65, 0x2c, 0x20, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x20, 0x76, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x77, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x32, 0x32,
	0x12, 0x76, 0x0a, 0x33, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x63,
	0x65, 0x65, 0x64, 0x73, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x46, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x10, 0x15, 0x1a, 0x3d, 0xaa, 0xbe, 0x92, 0x01, 0x2f,
	0x0a, 0x2b, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x65, 0x78, 0x63, 0x65, 0x65,
	0x64, 0x73, 0x20, 0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x20, 0x66, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x20, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x68, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x32, 0x33, 0x12, 0x58, 0x0a, 0x27, 0x4d, 0x61, 0x67, 0x6e,
	0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65,
	0x63, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x66,
	0x4c, 0x61, 0x77, 0x10, 0x16, 0x1a, 0x2b, 0xaa, 0xbe, 0x92, 0x01, 0x1d, 0x0a, 0x19, 0x44, 0x65,
	0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x20, 0x6f, 0x66, 0x20, 0x6c, 0x61, 0x77, 0x10, 0x77, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31,
	0x32, 0x34, 0x12, 0x5c, 0x0a, 0x29, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x43,
	0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x10,
	0x17, 0x1a, 0x2d, 0xaa, 0xbe, 0x92, 0x01, 0x1f, 0x0a, 0x1b, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e,
	0x65, 0x2c, 0x20, 0x63, 0x61, 0x72, 0x64, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x65, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x32, 0x35,
	0x12, 0x6c, 0x0a, 0x31, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x75, 0x73,
	0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x65, 0x69,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x10, 0x18, 0x1a, 0x35, 0xaa, 0xbe, 0x92, 0x01, 0x27, 0x0a, 0x23,
	0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x73, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x20, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x65, 0x69, 0x74, 0x20, 0x63,
	0x61, 0x72, 0x64, 0x10, 0x77, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x32, 0x39, 0x12, 0x7b,
	0x0a, 0x2f, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x10, 0x19, 0x1a, 0x46, 0xaa, 0xbe, 0x92, 0x01, 0x38, 0x0a, 0x34, 0x44, 0x65, 0x63, 0x6c,
	0x69, 0x6e, 0x65, 0x2c, 0x20, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x20,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x20, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x36, 0x30, 0x12, 0x72, 0x0a, 0x2c, 0x4d,
	0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x57, 0x72, 0x6f, 0x6e, 0x67, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x10, 0x1a, 0x1a, 0x40, 0xaa,
	0xbe, 0x92, 0x01, 0x32, 0x0a, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x77,
	0x72, 0x6f, 0x6e, 0x67, 0x20, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x20, 0x50, 0x49, 0x4e, 0x2c,
	0x20, 0x72, 0x65, 0x74, 0x72, 0x79, 0x20, 0x61, 0x73, 0x20, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65,
	0x20, 0x74, 0x61, 0x70, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x36, 0x31, 0x12,
	0x5f, 0x0a, 0x2b, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x66, 0x65,
	0x63, 0x79, 0x63, 0x6c, 0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x1b,
	0x1a, 0x2e, 0xaa, 0xbe, 0x92, 0x01, 0x20, 0x0a, 0x1c, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65,
	0x2c, 0x20, 0x4c, 0x69, 0x66, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x20, 0x76, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x36, 0x32,
	0x12, 0x59, 0x0a, 0x28, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x1c, 0x1a, 0x2b,
	0xaa, 0xbe, 0x92, 0x01, 0x1d, 0x0a, 0x19, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x20, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x10, 0x77, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x36, 0x33, 0x12, 0x68, 0x0a, 0x2f, 0x4d,
	0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x46, 0x72, 0x61, 0x75, 0x64, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x1d,
	0x1a, 0x33, 0xaa, 0xbe, 0x92, 0x01, 0x25, 0x0a, 0x21, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65,
	0x2c, 0x20, 0x46, 0x72, 0x61, 0x75, 0x64, 0x2f, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x20, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x77, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x03, 0x31, 0x36, 0x34, 0x12, 0x5e, 0x0a, 0x29, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69,
	0x6e, 0x65, 0x42, 0x79, 0x43, 0x61, 0x72, 0x64, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x57, 0x69,
	0x73, 0x68, 0x10, 0x1e, 0x1a, 0x2f, 0xaa, 0xbe, 0x92, 0x01, 0x21, 0x0a, 0x1d, 0x44, 0x65, 0x63,
	0x6c, 0x69, 0x6e, 0x65, 0x2c, 0x20, 0x62, 0x79, 0x20, 0x63, 0x61, 0x72, 0x64, 0x68, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x27, 0x73, 0x20, 0x77, 0x69, 0x73, 0x68, 0x10, 0x64, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x03, 0x31, 0x38, 0x30, 0x12, 0x7b, 0x0a, 0x2f, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x41, 0x64, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x64, 0x4e, 0x6f, 0x4c,
	0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x10, 0x1f, 0x1a, 0x46, 0xaa, 0xbe, 0x92, 0x01,
	0x38, 0x0a, 0x34, 0x41, 0x64, 0x76, 0x69, 0x63, 0x65, 0x20, 0x61, 0x63, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x64, 0x2c, 0x20, 0x6e, 0x6f, 0x20, 0x66, 0x69, 0x6e, 0x61, 0x6e,
	0x63, 0x69, 0x61, 0x6c, 0x20, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x20, 0x61,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39,
	0x30, 0x30, 0x12, 0x7e, 0x0a, 0x35, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x41, 0x64, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x64, 0x4c, 0x69, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x10, 0x20, 0x1a, 0x43, 0xaa,
	0xbe, 0x92, 0x01, 0x35, 0x0a, 0x31, 0x41, 0x64, 0x76, 0x69, 0x63, 0x65, 0x20, 0x61, 0x63, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x64, 0x2c, 0x20, 0x66, 0x69, 0x6e, 0x61, 0x6e,
	0x63, 0x69, 0x61, 0x6c, 0x20, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x20, 0x61,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39,
	0x30, 0x31, 0x12, 0x6e, 0x0a, 0x2b, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x49,
	0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x10, 0x21, 0x1a, 0x3d, 0xaa, 0xbe, 0x92, 0x01, 0x2f, 0x0a, 0x2b, 0x44, 0x65, 0x63, 0x6c,
	0x69, 0x6e, 0x65, 0x20, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x3a, 0x20, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39,
	0x30, 0x32, 0x12, 0x66, 0x0a, 0x2a, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x45, 0x6e, 0x74, 0x65, 0x72, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x10, 0x22, 0x1a, 0x36, 0xaa, 0xbe, 0x92, 0x01, 0x28, 0x0a, 0x24, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x20, 0x72, 0x65, 0x2d, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10,
	0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39, 0x30, 0x33, 0x12, 0x60, 0x0a, 0x24, 0x4d, 0x61,
	0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x10, 0x23, 0x1a, 0x36, 0xaa, 0xbe, 0x92, 0x01, 0x28, 0x0a, 0x24, 0x44, 0x65, 0x63,
	0x6c, 0x69, 0x6e, 0x65, 0x20, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x20, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x3a, 0x20, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x20, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39, 0x30, 0x34, 0x12, 0x83, 0x01, 0x0a,
	0x32, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x73, 0x73, 0x75, 0x65, 0x72,
	0x4f, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x10, 0x24, 0x1a, 0x4b, 0xaa, 0xbe, 0x92, 0x01, 0x3d, 0x0a, 0x39, 0x44, 0x65,
	0x63, 0x6c, 0x69, 0x6e, 0x65, 0x20, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x20, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x3a, 0x20, 0x63, 0x61, 0x72, 0x64, 0x20, 0x69, 0x73, 0x73, 0x75, 0x65,
	0x72, 0x20, 0x6f, 0x72, 0x20, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x20, 0x69, 0x6e, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x76, 0x65, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39,
	0x30, 0x37, 0x12, 0x8f, 0x01, 0x0a, 0x2c, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65,
	0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x74, 0x46, 0x6f,
	0x75, 0x6e, 0x64, 0x10, 0x25, 0x1a, 0x5d, 0xaa, 0xbe, 0x92, 0x01, 0x4f, 0x0a, 0x4b, 0x44, 0x65,
	0x63, 0x6c, 0x69, 0x6e, 0x65, 0x20, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x20, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x3a, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x20, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x63, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x20, 0x62, 0x65, 0x20, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x20, 0x66, 0x6f,
	0x72, 0x20, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x03, 0x39, 0x30, 0x38, 0x12, 0x6c, 0x0a, 0x2a, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e,
	0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x10, 0x26, 0x1a, 0x3c, 0xaa, 0xbe, 0x92, 0x01, 0x2e, 0x0a, 0x2a, 0x44, 0x65, 0x63,
	0x6c, 0x69, 0x6e, 0x65, 0x20, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x20, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x3a, 0x20, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x20, 0x6d, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39,
	0x30, 0x39, 0x12, 0x6e, 0x0a, 0x28, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x49,
	0x73, 0x73, 0x75, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x10, 0x27,
	0x1a, 0x40, 0xaa, 0xbe, 0x92, 0x01, 0x32, 0x0a, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65,
	0x20, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a,
	0x20, 0x63, 0x61, 0x72, 0x64, 0x20, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x20, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x20, 0x6f, 0x66, 0x66, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39,
	0x31, 0x30, 0x12, 0x6c, 0x0a, 0x27, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x49,
	0x73, 0x73, 0x75, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x10, 0x28, 0x1a,
	0x3f, 0xaa, 0xbe, 0x92, 0x01, 0x31, 0x0a, 0x2d, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x20,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x20,
	0x63, 0x61, 0x72, 0x64, 0x20, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x20, 0x74, 0x69, 0x6d, 0x65,
	0x64, 0x20, 0x6f, 0x75, 0x74, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39, 0x31, 0x31,
	0x12, 0x71, 0x0a, 0x2a, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x73, 0x73,
	0x75, 0x65, 0x72, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x29,
	0x1a, 0x41, 0xaa, 0xbe, 0x92, 0x01, 0x33, 0x0a, 0x2f, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65,
	0x20, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a,
	0x20, 0x63, 0x61, 0x72, 0x64, 0x20, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x20, 0x75, 0x6e, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03,
	0x39, 0x31, 0x32, 0x12, 0x74, 0x0a, 0x2e, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65,
	0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x10, 0x2a, 0x1a, 0x40, 0xaa, 0xbe, 0x92, 0x01, 0x32, 0x0a, 0x2e,
	0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x20, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x20, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x20, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x10, 0x6a,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39, 0x31, 0x33, 0x12, 0x8d, 0x01, 0x0a, 0x2f, 0x4d, 0x61,
	0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x6f, 0x74, 0x54, 0x72, 0x61, 0x63, 0x65, 0x61,
	0x62, 0x6c, 0x65, 0x54, 0x6f, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x10, 0x2b, 0x1a,
	0x58, 0xaa, 0xbe, 0x92, 0x01, 0x4a, 0x0a, 0x46, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x20,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x20,
	0x6e, 0x6f, 0x74, 0x20, 0x61, 0x62, 0x6c, 0x65, 0x20, 0x74, 0x6f, 0x20, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x20, 0x62, 0x61, 0x63, 0x6b, 0x20, 0x74, 0x6f, 0x20, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x61, 0x6c, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x6a,
	0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39, 0x31, 0x34, 0x12, 0x83, 0x01, 0x0a, 0x2c, 0x4d, 0x61,
	0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x4b, 0x65, 0x79,
	0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x2c, 0x1a, 0x51, 0xaa, 0xbe,
	0x92, 0x01, 0x43, 0x0a, 0x3f, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x20, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x20, 0x6e, 0x6f, 0x20,
	0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x6b, 0x65,
	0x79, 0x73, 0x20, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x20, 0x66, 0x6f, 0x72,
	0x20, 0x75, 0x73, 0x65, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39, 0x31, 0x38, 0x12,
	0x7b, 0x0a, 0x2d, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4f, 0x75, 0x74, 0x4f, 0x66, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65,
	0x10, 0x2d, 0x1a, 0x48, 0xaa, 0xbe, 0x92, 0x01, 0x3a, 0x0a, 0x36, 0x44, 0x65, 0x63, 0x6c, 0x69,
	0x6e, 0x65, 0x20, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x3a, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x20, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x20, 0x6f, 0x75, 0x74, 0x20, 0x6f, 0x66, 0x20, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x65, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39, 0x32, 0x32, 0x12, 0x64, 0x0a, 0x29,
	0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x10, 0x2e, 0x1a, 0x35, 0xaa, 0xbe, 0x92,
	0x01, 0x27, 0x0a, 0x23, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x3a, 0x20, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x20, 0x69, 0x6e, 0x20, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39,
	0x32, 0x33, 0x12, 0x7b, 0x0a, 0x2a, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x10, 0x2f, 0x1a, 0x4b, 0xaa, 0xbe, 0x92, 0x01, 0x3d, 0x0a, 0x39, 0x44, 0x65, 0x63, 0x6c, 0x69,
	0x6e, 0x65, 0x20, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x20, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x3a, 0x20, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x6f, 0x66, 0x20,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x20, 0x61, 0x72, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39, 0x35, 0x30, 0x12,
	0x86, 0x01, 0x0a, 0x25, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x33, 0x44, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x55, 0x6e,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x30, 0x1a, 0x5b, 0xaa, 0xbe, 0x92,
	0x01, 0x4d, 0x0a, 0x49, 0x33, 0x44, 0x20, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x20, 0x61, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x73, 0x20,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x6c, 0x79, 0x20, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x20, 0x28, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x20, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x20, 0x3d, 0x20, 0x45, 0x29, 0x10, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x38, 0x31, 0x12, 0x77, 0x0a, 0x24, 0x4d, 0x61, 0x67, 0x6e,
	0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x33, 0x44,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x10, 0x31, 0x1a, 0x4d, 0xaa, 0xbe, 0x92, 0x01, 0x3f, 0x0a, 0x3b, 0x33, 0x44, 0x20, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x65, 0x20, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x20, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x20, 0x28, 0x41, 0x75, 0x74, 0x68,
	0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x20, 0x3d, 0x20, 0x4e, 0x29, 0x10, 0x65, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x31, 0x38,
	0x32, 0x12, 0x81, 0x01, 0x0a, 0x26, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x4e, 0x6f, 0x74, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x10, 0x32, 0x1a, 0x55,
	0xaa, 0xbe, 0x92, 0x01, 0x47, 0x0a, 0x43, 0x43, 0x6f, 0x75, 0x6c, 0x64, 0x20, 0x6e, 0x6f, 0x74,
	0x20, 0x6f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x20, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x74, 0x68, 0x69, 0x73, 0x20, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x20, 0x28, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x20, 0x6f, 0x72, 0x20,
	0x63, 0x61, 0x72, 0x64, 0x20, 0x74, 0x79, 0x70, 0x65, 0x29, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x03, 0x39, 0x34, 0x30, 0x12, 0x61, 0x0a, 0x2c, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x4f, 0x72, 0x43,
	0x6c, 0x6f, 0x73, 0x65, 0x64, 0x10, 0x33, 0x1a, 0x2f, 0xaa, 0xbe, 0x92, 0x01, 0x21, 0x0a, 0x1d,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x73, 0x75, 0x73, 0x70, 0x65, 0x6e,
	0x64, 0x65, 0x64, 0x20, 0x6f, 0x72, 0x20, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x10, 0x6a, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39, 0x34, 0x31, 0x12, 0xa2, 0x01, 0x0a, 0x2a, 0x4d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x4f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x49, 0x6e, 0x65,
	0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x10, 0x34, 0x1a, 0x72, 0xaa, 0xbe, 0x92, 0x01, 0x64,
	0x0a, 0x60, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x20, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x20, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x20, 0x69, 0x73,
	0x20, 0x69, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x20, 0x66, 0x6f, 0x72, 0x20,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x50, 0x41, 0x4e, 0x20, 0x28, 0x61, 0x74, 0x20, 0x6c, 0x65, 0x61,
	0x73, 0x74, 0x20, 0x6f, 0x6e, 0x65, 0x20, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x64,
	0x20, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x20, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x29, 0x10, 0x6a, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39, 0x34, 0x32, 0x12, 0xc8, 0x01,
	0x0a, 0x36, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x33, 0x44, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x72, 0x6f,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x35, 0x1a, 0x8b, 0x01, 0xaa, 0xbe, 0x92,
	0x01, 0x7d, 0x0a, 0x79, 0x43, 0x61, 0x72, 0x64, 0x20, 0x65, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x20, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x20, 0x69, 0x6e, 0x20, 0x33, 0x44, 0x20, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x20, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x20, 0x28, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x20,
	0x56, 0x65, 0x52, 0x65, 0x73, 0x3b, 0x20, 0x44, 0x53, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x3b, 0x20, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x20, 0x69, 0x73, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x70, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69,
	0x70, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x20, 0x69, 0x6e, 0x20, 0x33, 0x44, 0x29, 0x10, 0x65, 0xca,
	0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39, 0x34, 0x35, 0x12, 0x9c, 0x01, 0x0a, 0x2f, 0x4d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x33,
	0x44, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x50, 0x61, 0x52, 0x65, 0x73, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x36, 0x1a, 0x67,
	0xaa, 0xbe, 0x92, 0x01, 0x59, 0x0a, 0x55, 0x33, 0x44, 0x20, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65,
	0x20, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x20, 0x65, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x20, 0x66, 0x72, 0x6f, 0x6d, 0x20, 0x50, 0x61, 0x52, 0x65, 0x73, 0x20, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x20, 0x28, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x20, 0x50,
	0x61, 0x52, 0x65, 0x73, 0x3b, 0x20, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x20,
	0x63, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x29, 0x10, 0x79, 0xca, 0xa1,
	0xec, 0x8e, 0x02, 0x03, 0x39, 0x34, 0x36, 0x12, 0xab, 0x01, 0x0a, 0x20, 0x4d, 0x61, 0x67, 0x6e,
	0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x75,
	0x73, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46, 0x72, 0x61, 0x75, 0x64, 0x10, 0x37, 0x1a, 0x84,
	0x01, 0xaa, 0xbe, 0x92, 0x01, 0x76, 0x0a, 0x72, 0x53, 0x75, 0x73, 0x70, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x20, 0x66, 0x72, 0x61, 0x75, 0x64, 0x20, 0x28, 0x4d, 0x41, 0x47, 0x4e, 0x45, 0x54, 0x49,
	0x51, 0x20, 0x61, 0x6e, 0x74, 0x69, 0x66, 0x72, 0x61, 0x75, 0x64, 0x20, 0x73, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x20, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x20, 0x74, 0x68, 0x69, 0x73, 0x20,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x20, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x20, 0x79, 0x6f, 0x75, 0x72, 0x20, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2d, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x6d, 0x6f, 0x72, 0x65, 0x20, 0x69, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x29, 0x10, 0x77, 0xca, 0xa1, 0xec, 0x8e,
	0x02, 0x03, 0x39, 0x34, 0x38, 0x12, 0x4f, 0x0a, 0x22, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x41, 0x62, 0x61, 0x6e, 0x64,
	0x6f, 0x6e, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x10, 0x38, 0x1a, 0x27, 0xaa,
	0xbe, 0x92, 0x01, 0x19, 0x0a, 0x15, 0x41, 0x62, 0x61, 0x6e, 0x64, 0x6f, 0x6e, 0x65, 0x64, 0x20,
	0x28, 0x54, 0x69, 0x6d, 0x65, 0x64, 0x20, 0x6f, 0x75, 0x74, 0x29, 0x10, 0x6c, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x03, 0x39, 0x34, 0x39, 0x12, 0xd9, 0x01, 0x0a, 0x20, 0x4d, 0x61, 0x67, 0x6e, 0x65,
	0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x73, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x10, 0x39, 0x1a, 0xb2, 0x01,
	0xaa, 0xbe, 0x92, 0x01, 0xa3, 0x01, 0x0a, 0x9e, 0x01, 0x4d, 0x41, 0x47, 0x4e, 0x45, 0x54, 0x49,
	0x51, 0x20, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x20, 0x65, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65,
	0x64, 0x20, 0x28, 0x4d, 0x41, 0x47, 0x4e, 0x45, 0x54, 0x49, 0x51, 0x20, 0x61, 0x6e, 0x74, 0x69,
	0x66, 0x72, 0x61, 0x75, 0x64, 0x20, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x20, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x65, 0x64, 0x20, 0x74, 0x68, 0x69, 0x73, 0x20, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x20, 0x64, 0x75, 0x65, 0x20, 0x74, 0x6f, 0x20, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x20,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x20, 0x69, 0x6e, 0x20, 0x66,
	0x6f, 0x72, 0x63, 0x65, 0x2e, 0x20, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x20, 0x79, 0x6f,
	0x75, 0x72, 0x20, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x20, 0x66, 0x6f, 0x72, 0x20, 0x6d, 0x6f, 0x72, 0x65, 0x20, 0x69, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x29, 0x10, 0x67, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x39,
	0x35, 0x31, 0x1a, 0x5a, 0xb2, 0xbe, 0x92, 0x01, 0x0d, 0x0a, 0x09, 0x75, 0x6e, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x10, 0x64, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x22, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82,
	0xec, 0x8e, 0x02, 0x1a, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0xff,
	0x01, 0x0a, 0x14, 0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x4d,
	0x6f, 0x64, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x4d, 0x61, 0x67, 0x6e, 0x65,
	0x74, 0x69, 0x71, 0x4d, 0x6f, 0x64, 0x65, 0x4e, 0x6f, 0x6e, 0x65, 0x33, 0x44, 0x53, 0x10, 0x00,
	0x1a, 0x17, 0xba, 0xbe, 0x92, 0x01, 0x12, 0x0a, 0x01, 0x34, 0x12, 0x0d, 0x4e, 0x6f, 0x6e, 0x2d,
	0x33, 0x44, 0x20, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x4d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x4d, 0x6f, 0x64, 0x65, 0x33, 0x44, 0x53, 0x10, 0x01, 0x1a, 0x13,
	0xba, 0xbe, 0x92, 0x01, 0x0e, 0x0a, 0x01, 0x36, 0x12, 0x09, 0x33, 0x44, 0x20, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x65, 0x12, 0x3f, 0x0a, 0x19, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x4d,
	0x6f, 0x64, 0x65, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x49, 0x6e,
	0x10, 0x02, 0x1a, 0x20, 0xba, 0xbe, 0x92, 0x01, 0x1b, 0x0a, 0x01, 0x39, 0x12, 0x16, 0x43, 0x61,
	0x72, 0x64, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x20, 0x4e, 0x6f, 0x74, 0x20, 0x50, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x74, 0x1a, 0x4a, 0xc2, 0xbe, 0x92, 0x01, 0x0b, 0x12, 0x09, 0x75, 0x6e, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x1b, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82, 0xec, 0x8e, 0x02, 0x13, 0x6d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x32, 0xc2, 0x13, 0x0a, 0x08, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x12, 0x82, 0x01,
	0x0a, 0x05, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x12, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x1a, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50,
	0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x0d, 0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50,
	0x61, 0x79, 0x49, 0x6e, 0x12, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x4f, 0x6e, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x8f, 0x01, 0x0a, 0x0e, 0x54, 0x68, 0x72, 0x65,
	0x65, 0x44, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x12, 0x3c, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x0d, 0x54, 0x68,
	0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x3e, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65,
	0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x53, 0x52, 0x65,
	0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x85,
	0x01, 0x0a, 0x06, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x12, 0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0xad, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x42, 0x61,
	0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42,
	0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xcb, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x42, 0x61,
	0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x55, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x12, 0x50, 0x2e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x51, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x55, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x06, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x12, 0x37,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x8e, 0x01, 0x0a, 0x09, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x61,
	0x79, 0x12, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x1a, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x22, 0x00, 0x12, 0x8b, 0x01, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x50, 0x61,
	0x79, 0x12, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x70, 0x70,
	0x6c, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x1a, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x70, 0x70, 0x6c,
	0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x0d, 0x54, 0x77, 0x6f, 0x53, 0x74, 0x61, 0x67, 0x65, 0x50,
	0x61, 0x79, 0x49, 0x6e, 0x12, 0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x54, 0x77, 0x6f, 0x53, 0x74, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x54, 0x77, 0x6f, 0x53, 0x74, 0x61, 0x67, 0x65, 0x50, 0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x06, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x12, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x06, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x12, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x09, 0x4d, 0x61, 0x6b, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50,
	0x61, 0x79, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a,
	0x3b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x49, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x00, 0x12, 0x7a,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a,
	0x47, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9b, 0x01, 0x0a, 0x10, 0x52,
	0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x56, 0x69, 0x73, 0x61, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x12,
	0x41, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c,
	0x76, 0x65, 0x56, 0x69, 0x73, 0x61, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x6c, 0x76, 0x65, 0x56, 0x69, 0x73, 0x61, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9a, 0x01, 0x0a, 0x0d, 0x50, 0x61, 0x79,
	0x4f, 0x75, 0x74, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x42, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74, 0x42, 0x79, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x43,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x75, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x22, 0x00, 0x3a, 0x88, 0x01, 0x0a, 0x15, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74,
	0x69, 0x71, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xe1, 0xa7, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71,
	0x2e, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x2e, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74,
	0x69, 0x71, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x52, 0x13, 0x6d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x92, 0x01, 0x0a, 0x1d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6d, 0x61, 0x67,
	0x6e, 0x65, 0x74, 0x69, 0x71, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xe2, 0xa7, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x2e, 0x6d,
	0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x2e, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x66, 0x52, 0x1a, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x9c, 0x01, 0x0a, 0x1d, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74,
	0x69, 0x71, 0x5f, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe3, 0xa7, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x2e, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x2e, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x45,
	0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x66, 0x52, 0x19, 0x6d, 0x61, 0x67, 0x6e, 0x65,
	0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0xa6, 0x01, 0x0a, 0x25, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x5f, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f,
	0x65, 0x72, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe4, 0xa7, 0x12,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x2e, 0x6d, 0x61, 0x67, 0x6e, 0x65,
	0x74, 0x69, 0x71, 0x2e, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c,
	0x74, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x66, 0x52, 0x20, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x46, 0x61, 0x75, 0x6c,
	0x74, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x95, 0x01,
	0x0a, 0x1a, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0xe5, 0xa7, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x2e, 0x6d, 0x61,
	0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x2e, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x66, 0x52, 0x17, 0x6d, 0x61,
	0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x9f, 0x01, 0x0a, 0x22, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe6, 0xa7, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x2e, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x2e, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x66, 0x52, 0x1e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x82, 0x01, 0x0a, 0x13, 0x6d, 0x61, 0x67, 0x6e,
	0x65, 0x74, 0x69, 0x71, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xe7, 0xa7, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71,
	0x2e, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x2e, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74,
	0x69, 0x71, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x66, 0x52, 0x11, 0x6d, 0x61, 0x67, 0x6e, 0x65,
	0x74, 0x69, 0x71, 0x4d, 0x6f, 0x64, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x8c, 0x01, 0x0a,
	0x1b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe8, 0xa7, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x2e, 0x6d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69,
	0x71, 0x2e, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74, 0x69, 0x71, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x66, 0x52, 0x18, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4d, 0x61, 0x67, 0x6e, 0x65, 0x74,
	0x69, 0x71, 0x4d, 0x6f, 0x64, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x2d, 0x5a, 0x2b, 0x67,
	0x69, 0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69,
	0x76, 0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_magnetiq_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_magnetiq_proto_rawDescData []byte
)

func file_inner_processing_grpc_magnetiq_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_magnetiq_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_magnetiq_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_magnetiq_proto_rawDesc), len(file_inner_processing_grpc_magnetiq_proto_rawDesc)))
	})
	return file_inner_processing_grpc_magnetiq_proto_rawDescData
}

var file_inner_processing_grpc_magnetiq_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_inner_processing_grpc_magnetiq_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_inner_processing_grpc_magnetiq_proto_goTypes = []any{
	(EnumMagnetiqStatus)(0),                         // 0: processing.magnetiq.magnetiq.EnumMagnetiqStatus
	(MagnetiqFaultErrCode)(0),                       // 1: processing.magnetiq.magnetiq.MagnetiqFaultErrCode
	(EnumMagnetiqActionCode)(0),                     // 2: processing.magnetiq.magnetiq.EnumMagnetiqActionCode
	(EnumMagnetiqModeCode)(0),                       // 3: processing.magnetiq.magnetiq.EnumMagnetiqModeCode
	(*MagnetiqStatusRef)(nil),                       // 4: processing.magnetiq.magnetiq.MagnetiqStatusRef
	(*MagnetiqFaultErrCodeRef)(nil),                 // 5: processing.magnetiq.magnetiq.MagnetiqFaultErrCodeRef
	(*MagnetiqActionCodeRef)(nil),                   // 6: processing.magnetiq.magnetiq.MagnetiqActionCodeRef
	(*MagnetiqModeRef)(nil),                         // 7: processing.magnetiq.magnetiq.MagnetiqModeRef
	(EnumTransactionStatus)(0),                      // 8: processing.transaction.transaction_status.EnumTransactionStatus
	(IntegrationError)(0),                           // 9: processing.integration.integration.IntegrationError
	(*descriptorpb.EnumValueOptions)(nil),           // 10: google.protobuf.EnumValueOptions
	(*descriptorpb.EnumOptions)(nil),                // 11: google.protobuf.EnumOptions
	(*PayInRequestData)(nil),                        // 12: processing.multiacquiring.multiacquiring.PayInRequestData
	(*OneClickPayInRequestData)(nil),                // 13: processing.multiacquiring.multiacquiring.OneClickPayInRequestData
	(*ThreeDSRequestData)(nil),                      // 14: processing.multiacquiring.multiacquiring.ThreeDSRequestData
	(*ThreeDSResumeRequest)(nil),                    // 15: processing.multiacquiring.multiacquiring.ThreeDSResumeRequest
	(*PayOutRequestData)(nil),                       // 16: processing.multiacquiring.multiacquiring.PayOutRequestData
	(*BankTransactionStatusRequest)(nil),            // 17: processing.multiacquiring.multiacquiring.BankTransactionStatusRequest
	(*BankTransactionStatusUnformatedRequest)(nil),  // 18: processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest
	(*RefundRequest)(nil),                           // 19: processing.multiacquiring.multiacquiring.RefundRequest
	(*GooglePayRequestData)(nil),                    // 20: processing.multiacquiring.multiacquiring.GooglePayRequestData
	(*ApplePayRequestData)(nil),                     // 21: processing.multiacquiring.multiacquiring.ApplePayRequestData
	(*TwoStagePayInRequest)(nil),                    // 22: processing.multiacquiring.multiacquiring.TwoStagePayInRequest
	(*ChargeRequest)(nil),                           // 23: processing.multiacquiring.multiacquiring.ChargeRequest
	(*CancelRequest)(nil),                           // 24: processing.multiacquiring.multiacquiring.CancelRequest
	(*emptypb.Empty)(nil),                           // 25: google.protobuf.Empty
	(*ResolveVisaAliasRequest)(nil),                 // 26: processing.multiacquiring.multiacquiring.ResolveVisaAliasRequest
	(*PayOutByPhoneRequestData)(nil),                // 27: processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData
	(*PayInResponseData)(nil),                       // 28: processing.multiacquiring.multiacquiring.PayInResponseData
	(*ThreeDSResponseData)(nil),                     // 29: processing.multiacquiring.multiacquiring.ThreeDSResponseData
	(*ThreeDSResumeResponse)(nil),                   // 30: processing.multiacquiring.multiacquiring.ThreeDSResumeResponse
	(*PayOutResponseData)(nil),                      // 31: processing.multiacquiring.multiacquiring.PayOutResponseData
	(*BankTransactionStatusResponse)(nil),           // 32: processing.multiacquiring.multiacquiring.BankTransactionStatusResponse
	(*BankTransactionStatusUnformatedResponse)(nil), // 33: processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedResponse
	(*RefundResponse)(nil),                          // 34: processing.multiacquiring.multiacquiring.RefundResponse
	(*GooglePayResponseData)(nil),                   // 35: processing.multiacquiring.multiacquiring.GooglePayResponseData
	(*ApplePayResponseData)(nil),                    // 36: processing.multiacquiring.multiacquiring.ApplePayResponseData
	(*TwoStagePayInResponse)(nil),                   // 37: processing.multiacquiring.multiacquiring.TwoStagePayInResponse
	(*ChargeResponse)(nil),                          // 38: processing.multiacquiring.multiacquiring.ChargeResponse
	(*CancelResponse)(nil),                          // 39: processing.multiacquiring.multiacquiring.CancelResponse
	(*GetAcquirerIdentifierResponse)(nil),           // 40: processing.multiacquiring.multiacquiring.GetAcquirerIdentifierResponse
	(*ResolveVisaAliasResponse)(nil),                // 41: processing.multiacquiring.multiacquiring.ResolveVisaAliasResponse
	(*PayOutResponseByPhoneData)(nil),               // 42: processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData
}
var file_inner_processing_grpc_magnetiq_proto_depIdxs = []int32{
	8,  // 0: processing.magnetiq.magnetiq.MagnetiqStatusRef.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	9,  // 1: processing.magnetiq.magnetiq.MagnetiqFaultErrCodeRef.integration_error:type_name -> processing.integration.integration.IntegrationError
	9,  // 2: processing.magnetiq.magnetiq.MagnetiqActionCodeRef.integration_error:type_name -> processing.integration.integration.IntegrationError
	10, // 3: processing.magnetiq.magnetiq.magnetiq_status_value:extendee -> google.protobuf.EnumValueOptions
	11, // 4: processing.magnetiq.magnetiq.default_magnetiq_status_value:extendee -> google.protobuf.EnumOptions
	10, // 5: processing.magnetiq.magnetiq.magnetiq_fault_err_code_value:extendee -> google.protobuf.EnumValueOptions
	11, // 6: processing.magnetiq.magnetiq.default_magnetiq_fault_err_code_value:extendee -> google.protobuf.EnumOptions
	10, // 7: processing.magnetiq.magnetiq.magnetiq_action_code_value:extendee -> google.protobuf.EnumValueOptions
	11, // 8: processing.magnetiq.magnetiq.default_magnetiq_action_code_value:extendee -> google.protobuf.EnumOptions
	10, // 9: processing.magnetiq.magnetiq.magnetiq_mode_value:extendee -> google.protobuf.EnumValueOptions
	11, // 10: processing.magnetiq.magnetiq.default_magnetiq_mode_value:extendee -> google.protobuf.EnumOptions
	4,  // 11: processing.magnetiq.magnetiq.magnetiq_status_value:type_name -> processing.magnetiq.magnetiq.MagnetiqStatusRef
	4,  // 12: processing.magnetiq.magnetiq.default_magnetiq_status_value:type_name -> processing.magnetiq.magnetiq.MagnetiqStatusRef
	5,  // 13: processing.magnetiq.magnetiq.magnetiq_fault_err_code_value:type_name -> processing.magnetiq.magnetiq.MagnetiqFaultErrCodeRef
	5,  // 14: processing.magnetiq.magnetiq.default_magnetiq_fault_err_code_value:type_name -> processing.magnetiq.magnetiq.MagnetiqFaultErrCodeRef
	6,  // 15: processing.magnetiq.magnetiq.magnetiq_action_code_value:type_name -> processing.magnetiq.magnetiq.MagnetiqActionCodeRef
	6,  // 16: processing.magnetiq.magnetiq.default_magnetiq_action_code_value:type_name -> processing.magnetiq.magnetiq.MagnetiqActionCodeRef
	7,  // 17: processing.magnetiq.magnetiq.magnetiq_mode_value:type_name -> processing.magnetiq.magnetiq.MagnetiqModeRef
	7,  // 18: processing.magnetiq.magnetiq.default_magnetiq_mode_value:type_name -> processing.magnetiq.magnetiq.MagnetiqModeRef
	12, // 19: processing.magnetiq.magnetiq.Magnetiq.PayIn:input_type -> processing.multiacquiring.multiacquiring.PayInRequestData
	13, // 20: processing.magnetiq.magnetiq.Magnetiq.OneClickPayIn:input_type -> processing.multiacquiring.multiacquiring.OneClickPayInRequestData
	14, // 21: processing.magnetiq.magnetiq.Magnetiq.ThreeDSConfirm:input_type -> processing.multiacquiring.multiacquiring.ThreeDSRequestData
	15, // 22: processing.magnetiq.magnetiq.Magnetiq.ThreeDSResume:input_type -> processing.multiacquiring.multiacquiring.ThreeDSResumeRequest
	16, // 23: processing.magnetiq.magnetiq.Magnetiq.PayOut:input_type -> processing.multiacquiring.multiacquiring.PayOutRequestData
	17, // 24: processing.magnetiq.magnetiq.Magnetiq.GetBankTransactionStatus:input_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusRequest
	18, // 25: processing.magnetiq.magnetiq.Magnetiq.GetBankTransactionStatusUnformated:input_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedRequest
	19, // 26: processing.magnetiq.magnetiq.Magnetiq.Refund:input_type -> processing.multiacquiring.multiacquiring.RefundRequest
	20, // 27: processing.magnetiq.magnetiq.Magnetiq.GooglePay:input_type -> processing.multiacquiring.multiacquiring.GooglePayRequestData
	21, // 28: processing.magnetiq.magnetiq.Magnetiq.ApplePay:input_type -> processing.multiacquiring.multiacquiring.ApplePayRequestData
	22, // 29: processing.magnetiq.magnetiq.Magnetiq.TwoStagePayIn:input_type -> processing.multiacquiring.multiacquiring.TwoStagePayInRequest
	23, // 30: processing.magnetiq.magnetiq.Magnetiq.Charge:input_type -> processing.multiacquiring.multiacquiring.ChargeRequest
	24, // 31: processing.magnetiq.magnetiq.Magnetiq.Cancel:input_type -> processing.multiacquiring.multiacquiring.CancelRequest
	12, // 32: processing.magnetiq.magnetiq.Magnetiq.MakeToken:input_type -> processing.multiacquiring.multiacquiring.PayInRequestData
	25, // 33: processing.magnetiq.magnetiq.Magnetiq.GetAcquirerIdentifier:input_type -> google.protobuf.Empty
	26, // 34: processing.magnetiq.magnetiq.Magnetiq.ResolveVisaAlias:input_type -> processing.multiacquiring.multiacquiring.ResolveVisaAliasRequest
	27, // 35: processing.magnetiq.magnetiq.Magnetiq.PayOutByPhone:input_type -> processing.multiacquiring.multiacquiring.PayOutByPhoneRequestData
	28, // 36: processing.magnetiq.magnetiq.Magnetiq.PayIn:output_type -> processing.multiacquiring.multiacquiring.PayInResponseData
	28, // 37: processing.magnetiq.magnetiq.Magnetiq.OneClickPayIn:output_type -> processing.multiacquiring.multiacquiring.PayInResponseData
	29, // 38: processing.magnetiq.magnetiq.Magnetiq.ThreeDSConfirm:output_type -> processing.multiacquiring.multiacquiring.ThreeDSResponseData
	30, // 39: processing.magnetiq.magnetiq.Magnetiq.ThreeDSResume:output_type -> processing.multiacquiring.multiacquiring.ThreeDSResumeResponse
	31, // 40: processing.magnetiq.magnetiq.Magnetiq.PayOut:output_type -> processing.multiacquiring.multiacquiring.PayOutResponseData
	32, // 41: processing.magnetiq.magnetiq.Magnetiq.GetBankTransactionStatus:output_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusResponse
	33, // 42: processing.magnetiq.magnetiq.Magnetiq.GetBankTransactionStatusUnformated:output_type -> processing.multiacquiring.multiacquiring.BankTransactionStatusUnformatedResponse
	34, // 43: processing.magnetiq.magnetiq.Magnetiq.Refund:output_type -> processing.multiacquiring.multiacquiring.RefundResponse
	35, // 44: processing.magnetiq.magnetiq.Magnetiq.GooglePay:output_type -> processing.multiacquiring.multiacquiring.GooglePayResponseData
	36, // 45: processing.magnetiq.magnetiq.Magnetiq.ApplePay:output_type -> processing.multiacquiring.multiacquiring.ApplePayResponseData
	37, // 46: processing.magnetiq.magnetiq.Magnetiq.TwoStagePayIn:output_type -> processing.multiacquiring.multiacquiring.TwoStagePayInResponse
	38, // 47: processing.magnetiq.magnetiq.Magnetiq.Charge:output_type -> processing.multiacquiring.multiacquiring.ChargeResponse
	39, // 48: processing.magnetiq.magnetiq.Magnetiq.Cancel:output_type -> processing.multiacquiring.multiacquiring.CancelResponse
	28, // 49: processing.magnetiq.magnetiq.Magnetiq.MakeToken:output_type -> processing.multiacquiring.multiacquiring.PayInResponseData
	40, // 50: processing.magnetiq.magnetiq.Magnetiq.GetAcquirerIdentifier:output_type -> processing.multiacquiring.multiacquiring.GetAcquirerIdentifierResponse
	41, // 51: processing.magnetiq.magnetiq.Magnetiq.ResolveVisaAlias:output_type -> processing.multiacquiring.multiacquiring.ResolveVisaAliasResponse
	42, // 52: processing.magnetiq.magnetiq.Magnetiq.PayOutByPhone:output_type -> processing.multiacquiring.multiacquiring.PayOutResponseByPhoneData
	36, // [36:53] is the sub-list for method output_type
	19, // [19:36] is the sub-list for method input_type
	11, // [11:19] is the sub-list for extension type_name
	3,  // [3:11] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_magnetiq_proto_init() }
func file_inner_processing_grpc_magnetiq_proto_init() {
	if File_inner_processing_grpc_magnetiq_proto != nil {
		return
	}
	file_inner_processing_grpc_multiacquiring_proto_init()
	file_inner_processing_grpc_transaction_status_proto_init()
	file_inner_processing_grpc_integration_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_magnetiq_proto_rawDesc), len(file_inner_processing_grpc_magnetiq_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   4,
			NumExtensions: 8,
			NumServices:   1,
		},
		GoTypes:           file_inner_processing_grpc_magnetiq_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_magnetiq_proto_depIdxs,
		EnumInfos:         file_inner_processing_grpc_magnetiq_proto_enumTypes,
		MessageInfos:      file_inner_processing_grpc_magnetiq_proto_msgTypes,
		ExtensionInfos:    file_inner_processing_grpc_magnetiq_proto_extTypes,
	}.Build()
	File_inner_processing_grpc_magnetiq_proto = out.File
	file_inner_processing_grpc_magnetiq_proto_goTypes = nil
	file_inner_processing_grpc_magnetiq_proto_depIdxs = nil
}
