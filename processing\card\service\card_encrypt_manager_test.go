package service

import (
	"context"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/encryptor"

	"github.com/stretchr/testify/require"

	"git.local/sensitive/processing/card/schema"
)

func TestDecryptPayInCard(t *testing.T) {
	type keyPair struct {
		privPEM string
		pub     *rsa.PublicKey
	}

	genKey := func() keyPair {
		key, err := rsa.GenerateKey(rand.Reader, 2048)
		require.NoError(t, err)
		block := &pem.Block{Type: "RSA PRIVATE KEY", Bytes: x509.MarshalPKCS1PrivateKey(key)}
		return keyPair{
			privPEM: string(pem.EncodeToMemory(block)),
			pub:     &key.PublicKey,
		}
	}

	encrypt := func(pub *rsa.PublicKey, data []byte) []byte {
		c, err := rsa.EncryptPKCS1v15(rand.Reader, pub, data)
		require.NoError(t, err)
		return c
	}

	badPair := genKey()
	alienPair := genKey()
	rsaErrCipher := encrypt(alienPair.pub, []byte("whatever"))

	jsonErrPair := genKey()
	jsonErrCipher := encrypt(jsonErrPair.pub, []byte("not-a-json"))

	refCard := &schema.DecryptedCard{
		Pan:      "****************",
		ExpMonth: "04",
		ExpYear:  "29",
		Cvc:      "123",
		FullName: "TEST USER",
	}
	refJSON, err := json.Marshal(refCard)
	if err != nil {
		t.Fatal(err)
	}

	okPair := genKey()
	okCipher := encrypt(okPair.pub, refJSON)

	type encryptOp struct {
		input     encryptor.DecryptedCard
		output    encryptor.EncryptedCard
		outputErr error
	}

	tests := []struct {
		name       string
		req        []byte
		want       encryptor.EncryptedCard
		wantErr    error
		encrypt    encryptOp
		privateKey string
	}{
		{
			name:       "empty_private_key",
			req:        []byte(""),
			want:       encryptor.EncryptedCard{},
			wantErr:    goerr.ErrPrivateKey,
			encrypt:    encryptOp{},
			privateKey: "",
		},
		{
			name:    "parse_private_key_error",
			req:     []byte(""),
			want:    encryptor.EncryptedCard{},
			wantErr: errors.New("x509"),
			encrypt: encryptOp{},
			// this private key is wrong one
			privateKey: "-----BEGIN RSA PRIVATE KEY-----\nMIICHgIBAAKCAQEAkvro39f9TwktskxcxiDB4fzHE4AXDYe2FkWVVMGjlxGUVag3\nvK9WPlIKgoGQJvoBnKXbgoM+544+F13JdYTivU9ym90SuBn4KpnZcntLitZWbCs1\nzoMS6L1Xp3E6yjgg0fheYTQ1Ux2GZ92EL8eTQtxHijtZSDVRSEShzUlmD4hErYuz\nO9qKmbRxBXR0BSec1PyFIrp64oj2HEPcKUijgmHy1LUtAkc3Jy7f8jmxoaJKzecb\nRTv4WsV8iDQMD+OOCDa3ZZ6DRJknXsZWqHtfjtoJz0wo7O+E5xa/mkBjyEMeQQm8\nVHzcuNu6DslCrUQTpZ2sRJL8c90m/Knp56rVbwIBAAKCAQAFzLTHsGQkbD5LXMwE\nXWKJIkpQsKs4M3Sx6Ej+MS3dVio+PwcEAP8sMlyLzcT8ZL0Pi1PgEIG47Vnx3heG\n9Zlk0u+4yr5kWVH7jFqSM99FOTPrzMCwx2dFm8MLrmIdfJ1+64f92euIvg+BpbID\nmKJs7vg7sh/Rw5jTezHPQegvEKgWPGaJIfCeyeSZsOiWmfypzjfOPZEaHRo99h46\n96T4YmG12Rm0TL6O3s3+EW0T20jrwoddoG8yrWKXSgjbliK4yknmR7ZZR+F5atJd\n7fxm0tBl+XaqBBUIu2QQV3262ll3QQPFXPjJ5SpXIC+SIG/Mk0FH9ngKpuM0g+Mi\n20OxAgEAAgEAAgEAAgEAAgEA\n-----END RSA PRIVATE KEY-----",
		},
		{
			name:       "decrypt_error",
			req:        rsaErrCipher,
			want:       encryptor.EncryptedCard{},
			wantErr:    errors.New("decryption error"),
			privateKey: badPair.privPEM,
		},
		{
			name:       "json_error",
			req:        jsonErrCipher,
			want:       encryptor.EncryptedCard{},
			wantErr:    errors.New("invalid character"),
			privateKey: jsonErrPair.privPEM,
		},
		{
			name: "success",
			req:  okCipher,
			want: encryptor.EncryptedCard{
				Number:          []byte(refCard.Pan),
				ExpirationMonth: []byte(refCard.ExpMonth),
				ExpirationYear:  []byte(refCard.ExpYear),
				CVC:             []byte(refCard.Cvc),
				HolderName:      []byte(refCard.FullName),
			},
			encrypt: encryptOp{
				input: encryptor.DecryptedCard{
					Number:          "****************",
					ExpirationMonth: "04",
					ExpirationYear:  "29",
					CVC:             "123",
					HolderName:      "TEST USER",
				},
				output: encryptor.EncryptedCard{
					Number:          []byte(refCard.Pan),
					ExpirationMonth: []byte(refCard.ExpMonth),
					ExpirationYear:  []byte(refCard.ExpYear),
					CVC:             []byte(refCard.Cvc),
					HolderName:      []byte(refCard.FullName),
				},
				outputErr: nil,
			},
			privateKey: okPair.privPEM,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			encryptorMock := encryptor.NewMockEncryptor(ctrl)

			s := CardEncryptManagerService{
				repos:      nil,
				privateKey: tt.privateKey,
				encryptor:  encryptorMock,
			}

			encryptorMock.EXPECT().EncryptCard(tt.encrypt.input).
				Return(tt.encrypt.output, tt.encrypt.outputErr).AnyTimes()

			res, err := s.DecryptPayInCard(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}

func TestDecryptCard(t *testing.T) {

	type keyPair struct {
		privPEM string
		pub     *rsa.PublicKey
	}

	genKey := func() keyPair {
		key, err := rsa.GenerateKey(rand.Reader, 2048)
		require.NoError(t, err)
		block := &pem.Block{Type: "RSA PRIVATE KEY", Bytes: x509.MarshalPKCS1PrivateKey(key)}
		return keyPair{
			privPEM: string(pem.EncodeToMemory(block)),
			pub:     &key.PublicKey,
		}
	}

	encrypt := func(pub *rsa.PublicKey, data []byte) []byte {
		c, err := rsa.EncryptPKCS1v15(rand.Reader, pub, data)
		require.NoError(t, err)
		return c
	}

	badPair := genKey()
	alienPair := genKey()
	rsaErrCipher := encrypt(alienPair.pub, []byte("whatever"))

	jsonErrPair := genKey()
	jsonErrCipher := encrypt(jsonErrPair.pub, []byte("not-a-json"))

	refCard := &schema.DecryptedCard{
		Pan:      "****************",
		ExpMonth: "04",
		ExpYear:  "29",
		Cvc:      "123",
		FullName: "TEST USER",
	}
	refJSON, err := json.Marshal(refCard)
	if err != nil {
		t.Fatal(err)
	}

	okPair := genKey()
	okCipher := encrypt(okPair.pub, refJSON)

	type encryptOp struct {
		input     encryptor.DecryptedCard
		output    encryptor.EncryptedCard
		outputErr error
	}

	tests := []struct {
		name       string
		req        []byte
		want       encryptor.EncryptedCard
		wantErr    error
		encrypt    encryptOp
		privateKey string
	}{
		{
			name:       "empty_private_key",
			req:        []byte(""),
			want:       encryptor.EncryptedCard{},
			wantErr:    goerr.ErrPrivateKey,
			privateKey: "",
		},
		{
			name:    "parse_private_key_error",
			req:     []byte(""),
			want:    encryptor.EncryptedCard{},
			wantErr: errors.New("x509"),
			// this private key is wrong one
			privateKey: "-----BEGIN RSA PRIVATE KEY-----\nMIICHgIBAAKCAQEAkvro39f9TwktskxcxiDB4fzHE4AXDYe2FkWVVMGjlxGUVag3\nvK9WPlIKgoGQJvoBnKXbgoM+544+F13JdYTivU9ym90SuBn4KpnZcntLitZWbCs1\nzoMS6L1Xp3E6yjgg0fheYTQ1Ux2GZ92EL8eTQtxHijtZSDVRSEShzUlmD4hErYuz\nO9qKmbRxBXR0BSec1PyFIrp64oj2HEPcKUijgmHy1LUtAkc3Jy7f8jmxoaJKzecb\nRTv4WsV8iDQMD+OOCDa3ZZ6DRJknXsZWqHtfjtoJz0wo7O+E5xa/mkBjyEMeQQm8\nVHzcuNu6DslCrUQTpZ2sRJL8c90m/Knp56rVbwIBAAKCAQAFzLTHsGQkbD5LXMwE\nXWKJIkpQsKs4M3Sx6Ej+MS3dVio+PwcEAP8sMlyLzcT8ZL0Pi1PgEIG47Vnx3heG\n9Zlk0u+4yr5kWVH7jFqSM99FOTPrzMCwx2dFm8MLrmIdfJ1+64f92euIvg+BpbID\nmKJs7vg7sh/Rw5jTezHPQegvEKgWPGaJIfCeyeSZsOiWmfypzjfOPZEaHRo99h46\n96T4YmG12Rm0TL6O3s3+EW0T20jrwoddoG8yrWKXSgjbliK4yknmR7ZZR+F5atJd\n7fxm0tBl+XaqBBUIu2QQV3262ll3QQPFXPjJ5SpXIC+SIG/Mk0FH9ngKpuM0g+Mi\n20OxAgEAAgEAAgEAAgEAAgEA\n-----END RSA PRIVATE KEY-----",
		},
		{
			name:       "decrypt_error",
			req:        rsaErrCipher,
			want:       encryptor.EncryptedCard{},
			wantErr:    errors.New("decryption error"),
			privateKey: badPair.privPEM,
		},
		{
			name:       "json_error",
			req:        jsonErrCipher,
			want:       encryptor.EncryptedCard{},
			wantErr:    errors.New("invalid character"),
			privateKey: jsonErrPair.privPEM,
		},
		{
			name: "success",
			req:  okCipher,
			want: encryptor.EncryptedCard{
				Number:          []byte(refCard.Pan),
				ExpirationMonth: []byte(refCard.ExpMonth),
				ExpirationYear:  []byte(refCard.ExpYear),
				CVC:             []byte(refCard.Cvc),
				HolderName:      []byte(refCard.FullName),
			},
			encrypt: encryptOp{
				input: encryptor.DecryptedCard{
					Number:          refCard.Pan,
					ExpirationMonth: refCard.ExpMonth,
					ExpirationYear:  refCard.ExpYear,
					CVC:             refCard.Cvc,
					HolderName:      refCard.FullName,
				},
				output: encryptor.EncryptedCard{
					Number:          []byte(refCard.Pan),
					ExpirationMonth: []byte(refCard.ExpMonth),
					ExpirationYear:  []byte(refCard.ExpYear),
					CVC:             []byte(refCard.Cvc),
					HolderName:      []byte(refCard.FullName),
				},
				outputErr: nil,
			},
			privateKey: okPair.privPEM,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			encryptorMock := encryptor.NewMockEncryptor(ctrl)

			s := CardEncryptManagerService{
				repos:      nil,
				privateKey: tt.privateKey,
				encryptor:  encryptorMock,
			}

			encryptorMock.EXPECT().EncryptCard(tt.encrypt.input).
				Return(tt.encrypt.output, tt.encrypt.outputErr).AnyTimes()

			res, err := s.DecryptPayInCard(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.wantErr.Error())
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, res)
		})
	}
}
