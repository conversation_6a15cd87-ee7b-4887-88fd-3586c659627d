// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/multiordering.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Multiordering_GetPaymentOrder_FullMethodName       = "/processing.multiordering.multiordering.Multiordering/GetPaymentOrder"
	Multiordering_GetOrderingIdentifier_FullMethodName = "/processing.multiordering.multiordering.Multiordering/GetOrderingIdentifier"
)

// MultiorderingClient is the client API for Multiordering service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MultiorderingClient interface {
	GetPaymentOrder(ctx context.Context, in *GetPaymentOrderRequest, opts ...grpc.CallOption) (*GetPaymentOrderResponse, error)
	GetOrderingIdentifier(ctx context.Context, in *GetOrderingIdentifierRequest, opts ...grpc.CallOption) (*GetOrderingIdentifierResponse, error)
}

type multiorderingClient struct {
	cc grpc.ClientConnInterface
}

func NewMultiorderingClient(cc grpc.ClientConnInterface) MultiorderingClient {
	return &multiorderingClient{cc}
}

func (c *multiorderingClient) GetPaymentOrder(ctx context.Context, in *GetPaymentOrderRequest, opts ...grpc.CallOption) (*GetPaymentOrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPaymentOrderResponse)
	err := c.cc.Invoke(ctx, Multiordering_GetPaymentOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *multiorderingClient) GetOrderingIdentifier(ctx context.Context, in *GetOrderingIdentifierRequest, opts ...grpc.CallOption) (*GetOrderingIdentifierResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOrderingIdentifierResponse)
	err := c.cc.Invoke(ctx, Multiordering_GetOrderingIdentifier_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MultiorderingServer is the server API for Multiordering service.
// All implementations must embed UnimplementedMultiorderingServer
// for forward compatibility.
type MultiorderingServer interface {
	GetPaymentOrder(context.Context, *GetPaymentOrderRequest) (*GetPaymentOrderResponse, error)
	GetOrderingIdentifier(context.Context, *GetOrderingIdentifierRequest) (*GetOrderingIdentifierResponse, error)
	mustEmbedUnimplementedMultiorderingServer()
}

// UnimplementedMultiorderingServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMultiorderingServer struct{}

func (UnimplementedMultiorderingServer) GetPaymentOrder(context.Context, *GetPaymentOrderRequest) (*GetPaymentOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaymentOrder not implemented")
}
func (UnimplementedMultiorderingServer) GetOrderingIdentifier(context.Context, *GetOrderingIdentifierRequest) (*GetOrderingIdentifierResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderingIdentifier not implemented")
}
func (UnimplementedMultiorderingServer) mustEmbedUnimplementedMultiorderingServer() {}
func (UnimplementedMultiorderingServer) testEmbeddedByValue()                       {}

// UnsafeMultiorderingServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MultiorderingServer will
// result in compilation errors.
type UnsafeMultiorderingServer interface {
	mustEmbedUnimplementedMultiorderingServer()
}

func RegisterMultiorderingServer(s grpc.ServiceRegistrar, srv MultiorderingServer) {
	// If the following call pancis, it indicates UnimplementedMultiorderingServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Multiordering_ServiceDesc, srv)
}

func _Multiordering_GetPaymentOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiorderingServer).GetPaymentOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Multiordering_GetPaymentOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiorderingServer).GetPaymentOrder(ctx, req.(*GetPaymentOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Multiordering_GetOrderingIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderingIdentifierRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MultiorderingServer).GetOrderingIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Multiordering_GetOrderingIdentifier_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MultiorderingServer).GetOrderingIdentifier(ctx, req.(*GetOrderingIdentifierRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Multiordering_ServiceDesc is the grpc.ServiceDesc for Multiordering service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Multiordering_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.multiordering.multiordering.Multiordering",
	HandlerType: (*MultiorderingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPaymentOrder",
			Handler:    _Multiordering_GetPaymentOrder_Handler,
		},
		{
			MethodName: "GetOrderingIdentifier",
			Handler:    _Multiordering_GetOrderingIdentifier_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/multiordering.proto",
}
