package service

import (
	"context"
	"errors"
	"git.local/sensitive/innerpb/processing/grpc/grpcmock"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog/e2e"
	"git.local/sensitive/testsdk"
)

func TestGetAcquires(t *testing.T) {
	type getAllOp struct {
		input     uint64
		output    []model.Terminal
		outputErr error
	}

	type getAcquirersByIDOp struct {
		isCalled  bool
		input     []uint64
		output    []*model.Acquirer
		outputErr error
	}

	tests := []struct {
		name             string
		req              uint64
		want             []*model.Acquirer
		wantErr          error
		getAll           getAllOp
		getAcquirersByID getAcquirersByIDOp
	}{
		{
			name:    "error when getting all",
			req:     24,
			want:    nil,
			wantErr: errors.New("some error"),
			getAll: getAllOp{
				input:     24,
				output:    nil,
				outputErr: errors.New("some error"),
			},
		},
		{
			name:    "error when getting acquirer by id",
			req:     24,
			want:    nil,
			wantErr: errors.New("some another error"),
			getAll: getAllOp{
				input: 24,
				output: []model.Terminal{
					{
						ID:              22,
						AcquirerID:      1,
						Status:          1,
						EncryptedConfig: "AAAAAAAAAAAAAAAAAAAAACmWAaXWruscAToiSj3tmeWGkgJixFU+f2haCQGB7+rXa+cOfXkDHOxJ37G17/HjmaYtUVGwfmYDZJDQVb4UQprd6erkboic2rT5U5da8HBs",
					},
					{
						ID:              23,
						AcquirerID:      2,
						Status:          2,
						EncryptedConfig: "AAAAAAAAAAAAAAAAAAAAACgM70S19V48AFL7pceC+v3oYAvD2nAW5RCD160Vo6MOy7L9nBXQyCvPAt9zgtpBTrAHO53qtWAfdZCmADfo2gaAbMBvaHGQtSfHEEgauzsZKBLhv1nTGsjTqq9fWETzZn2tH/PxcKkc1eeNo530Rox2x05F3OwmY2VHr1dTLB+D",
					},
				},
				outputErr: nil,
			},
			getAcquirersByID: getAcquirersByIDOp{
				isCalled:  true,
				input:     []uint64{1, 2},
				output:    nil,
				outputErr: errors.New("some another error"),
			},
		},
		{
			name: "success",
			req:  24,
			want: []*model.Acquirer{
				{
					ID:        1,
					Code:      "bcc",
					Name:      "bank center credit",
					BankID:    74,
					CountryID: 22,
				},
				{
					ID:        2,
					Code:      "freedom",
					Name:      "freedom",
					BankID:    75,
					CountryID: 22,
				},
				{
					ID:        3,
					Code:      "ukb",
					Name:      "uk bank",
					BankID:    13,
					CountryID: 11,
				},
			},
			wantErr: nil,
			getAll: getAllOp{
				input: 24,
				output: []model.Terminal{
					{
						ID:              22,
						AcquirerID:      1,
						Status:          1,
						EncryptedConfig: "AAAAAAAAAAAAAAAAAAAAACmWAaXWruscAToiSj3tmeWGkgJixFU+f2haCQGB7+rXa+cOfXkDHOxJ37G17/HjmaYtUVGwfmYDZJDQVb4UQprd6erkboic2rT5U5da8HBs",
					},
					{
						ID:              23,
						AcquirerID:      2,
						Status:          2,
						EncryptedConfig: "AAAAAAAAAAAAAAAAAAAAACgM70S19V48AFL7pceC+v3oYAvD2nAW5RCD160Vo6MOy7L9nBXQyCvPAt9zgtpBTrAHO53qtWAfdZCmADfo2gaAbMBvaHGQtSfHEEgauzsZKBLhv1nTGsjTqq9fWETzZn2tH/PxcKkc1eeNo530Rox2x05F3OwmY2VHr1dTLB+D",
					},
				},
				outputErr: nil,
			},
			getAcquirersByID: getAcquirersByIDOp{
				isCalled: true,
				input:    []uint64{1, 2},
				output: []*model.Acquirer{
					{
						ID:        1,
						Code:      "bcc",
						Name:      "bank center credit",
						BankID:    74,
						CountryID: 22,
					},
					{
						ID:        2,
						Code:      "freedom",
						Name:      "freedom",
						BankID:    75,
						CountryID: 22,
					},
					{
						ID:        3,
						Code:      "ukb",
						Name:      "uk bank",
						BankID:    13,
						CountryID: 11,
					},
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()

			ctx, cancel := context.WithCancel(c)

			e2e.InitAppE2E(t, ctx, cancel, nil, nil, nil)

			terminalBasicDBMock := databasemocks.NewMockTerminalBasicer(ctrl)
			acquirerDBMock := databasemocks.NewMockAcquirer(ctrl)

			terminalBasicDBMock.EXPECT().GetAll(
				gomock.Any(),
				tt.getAll.input,
			).Return(tt.getAll.output, tt.getAll.outputErr).Times(1)

			if tt.getAcquirersByID.isCalled {
				acquirerDBMock.EXPECT().GetAcquirersById(
					gomock.Any(),
					tt.getAcquirersByID.input,
				).Return(tt.getAcquirersByID.output, tt.getAcquirersByID.outputErr).Times(1)
			}

			projectService := NewProjectService(
				terminalBasicDBMock,
				acquirerDBMock,
				nil,
				nil,
			)

			resp, err := projectService.GetAcquirers(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}

func TestGetTerminals(t *testing.T) {
	type getAllOp struct {
		input     uint64
		output    []model.Terminal
		outputErr error
	}

	tests := []struct {
		name    string
		req     uint64
		want    []*schema.TerminalResponse
		wantErr error
		getAll  getAllOp
	}{
		{
			name: "error when getting all",
			req:  24,
			getAll: getAllOp{
				input:     24,
				output:    nil,
				outputErr: errors.New("some error"),
			},
			wantErr: errors.New("some error"),
			want:    nil,
		},
		{
			name: "success",
			req:  24,
			getAll: getAllOp{
				input: 24,
				output: []model.Terminal{
					{
						ID:              22,
						AcquirerID:      1,
						Status:          1,
						EncryptedConfig: "AAAAAAAAAAAAAAAAAAAAACmWAaXWruscAToiSj3tmeWGkgJixFU+f2haCQGB7+rXa+cOfXkDHOxJ37G17/HjmaYtUVGwfmYDZJDQVb4UQprd6erkboic2rT5U5da8HBs",
					},
					{
						ID:              23,
						AcquirerID:      2,
						Status:          2,
						EncryptedConfig: "AAAAAAAAAAAAAAAAAAAAACgM70S19V48AFL7pceC+v3oYAvD2nAW5RCD160Vo6MOy7L9nBXQyCvPAt9zgtpBTrAHO53qtWAfdZCmADfo2gaAbMBvaHGQtSfHEEgauzsZKBLhv1nTGsjTqq9fWETzZn2tH/PxcKkc1eeNo530Rox2x05F3OwmY2VHr1dTLB+D",
					},
				},
				outputErr: nil,
			},
			wantErr: nil,
			want: []*schema.TerminalResponse{
				{
					ID:         22,
					AcquirerID: 1,
					Status:     1,
					Config:     "AAAAAAAAAAAAAAAAAAAAACmWAaXWruscAToiSj3tmeWGkgJixFU+f2haCQGB7+rXa+cOfXkDHOxJ37G17/HjmaYtUVGwfmYDZJDQVb4UQprd6erkboic2rT5U5da8HBs",
				},
				{
					ID:         23,
					AcquirerID: 2,
					Status:     2,
					Config:     "AAAAAAAAAAAAAAAAAAAAACgM70S19V48AFL7pceC+v3oYAvD2nAW5RCD160Vo6MOy7L9nBXQyCvPAt9zgtpBTrAHO53qtWAfdZCmADfo2gaAbMBvaHGQtSfHEEgauzsZKBLhv1nTGsjTqq9fWETzZn2tH/PxcKkc1eeNo530Rox2x05F3OwmY2VHr1dTLB+D",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()

			ctx, cancel := context.WithCancel(c)

			e2e.InitAppE2E(t, ctx, cancel, nil, nil, nil)

			terminalBasicDBMock := databasemocks.NewMockTerminalBasicer(ctrl)

			terminalBasicDBMock.EXPECT().GetAll(
				gomock.Any(),
				tt.getAll.input,
			).Return(tt.getAll.output, tt.getAll.outputErr).Times(1)

			projectService := NewProjectService(terminalBasicDBMock, nil, nil, nil)

			resp, err := projectService.GetTerminals(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)

		})
	}
}

func TestGetPaymentTypes(t *testing.T) {

	type getAllPaymentTypesOp struct {
		input     uint64
		output    []*model.ResponsePaymentType
		outputErr error
	}

	type getAllOp struct {
		isCalled  bool
		output    *grpc.TransactionTypeResponseV1
		outputErr error
	}

	tests := []struct {
		name               string
		req                uint64
		want               []*schema.ResponsePaymentType
		wantErr            error
		getAllPaymentTypes getAllPaymentTypesOp
		getAll             getAllOp
	}{
		{
			name: "error in get all payment types",
			req:  24,
			getAllPaymentTypes: getAllPaymentTypesOp{
				input:     24,
				outputErr: errors.New("some error"),
				output:    nil,
			},
			wantErr: errors.New("some error"),
			want:    nil,
		},
		{
			name: "error when address to get all transaction types by grpc",
			req:  24,
			getAllPaymentTypes: getAllPaymentTypesOp{
				input:     24,
				outputErr: nil,
				output: []*model.ResponsePaymentType{
					{
						ProjectID:         24,
						TransactionTypeID: 1,
					},
					{
						ProjectID:         24,
						TransactionTypeID: 2,
					},
					{
						ProjectID:         24,
						TransactionTypeID: 3,
					},
					{
						ProjectID:         24,
						TransactionTypeID: 4,
					},
				},
			},
			getAll: getAllOp{
				isCalled:  true,
				output:    nil,
				outputErr: errors.New("some another error"),
			},
			wantErr: errors.New("some another error"),
			want:    nil,
		},
		{
			name: "success",
			req:  24,
			getAllPaymentTypes: getAllPaymentTypesOp{
				input:     24,
				outputErr: nil,
				output: []*model.ResponsePaymentType{
					{
						ProjectID:         24,
						TransactionTypeID: 1,
					},
					{
						ProjectID:         24,
						TransactionTypeID: 2,
					},
					{
						ProjectID:         24,
						TransactionTypeID: 3,
					},
					{
						ProjectID:         24,
						TransactionTypeID: 4,
					},
				},
			},
			getAll: getAllOp{
				isCalled: true,
				output: &grpc.TransactionTypeResponseV1{
					Data: []*grpc.TransactionTypesResponseV1{
						{Id: testsdk.Ptr(uint64(1)), Code: testsdk.Ptr("in"), Name: testsdk.Ptr("Приём")},
						{Id: testsdk.Ptr(uint64(8)), Code: testsdk.Ptr("google_pay"), Name: testsdk.Ptr("Гугл Пэй")},
						{Id: testsdk.Ptr(uint64(4)), Code: testsdk.Ptr("one_click_pay_out"), Name: testsdk.Ptr("Рекуррентный вывод")},
						{Id: testsdk.Ptr(uint64(2)), Code: testsdk.Ptr("out"), Name: testsdk.Ptr("Вывод")},
						{Id: testsdk.Ptr(uint64(7)), Code: testsdk.Ptr("refund"), Name: testsdk.Ptr("Возврат")},
						{Id: testsdk.Ptr(uint64(6)), Code: testsdk.Ptr("card_link"), Name: testsdk.Ptr("Привязка карты")},
						{Id: testsdk.Ptr(uint64(3)), Code: testsdk.Ptr("one_click_pay_in"), Name: testsdk.Ptr("Рекуррентный приём")},
						{Id: testsdk.Ptr(uint64(5)), Code: testsdk.Ptr("apple_pay"), Name: testsdk.Ptr("Эпл Пэй")},
						{Id: testsdk.Ptr(uint64(9)), Code: testsdk.Ptr("two_stage_pay_in"), Name: testsdk.Ptr("Двухстадийный приём")},
						{Id: testsdk.Ptr(uint64(10)), Code: testsdk.Ptr("donation"), Name: testsdk.Ptr("Донат")},
					},
				},
				outputErr: nil,
			},
			wantErr: nil,
			want: []*schema.ResponsePaymentType{
				{
					ProjectID:         24,
					TransactionTypeID: 1,
					TransactionType: schema.TransactionType{
						Id:   1,
						Code: "in",
						Name: "Приём",
					},
				},
				{
					ProjectID:         24,
					TransactionTypeID: 2,
					TransactionType: schema.TransactionType{
						Id:   2,
						Code: "out",
						Name: "Вывод",
					},
				},
				{
					ProjectID:         24,
					TransactionTypeID: 3,
					TransactionType: schema.TransactionType{
						Id:   3,
						Code: "one_click_pay_in",
						Name: "Рекуррентный приём",
					},
				},
				{
					ProjectID:         24,
					TransactionTypeID: 4,
					TransactionType: schema.TransactionType{
						Id:   4,
						Code: "one_click_pay_out",
						Name: "Рекуррентный вывод",
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			terminalDBMock := databasemocks.NewMockTerminaler(ctrl)
			transactionTypeCliMock := grpcmock.NewMockTransactionTypeClient(ctrl)

			terminalDBMock.EXPECT().GetAllPaymentTypes(
				gomock.Any(),
				tt.getAllPaymentTypes.input,
			).Return(tt.getAllPaymentTypes.output, tt.getAllPaymentTypes.outputErr).Times(1)

			if tt.getAll.isCalled {
				transactionTypeCliMock.EXPECT().GetAll(
					gomock.Any(),
					nil, // or gomock.Any() because the passed value is actually nil
				).Return(tt.getAll.output, tt.getAll.outputErr).Times(1)
			}

			projectService := NewProjectService(nil, nil, terminalDBMock, transactionTypeCliMock)

			resp, err := projectService.GetPaymentTypes(context.Background(), tt.req)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}

func TestGetAcquirersByPaymentType(t *testing.T) {
	type getAllPaymentTypesOp struct {
		inputProjectID         uint64
		inputTransactionTypeID uint64
		output                 []*model.Terminal
		outputErr              error
	}

	type getAcquirersByIDOp struct {
		isCalled  bool
		input     []uint64
		output    []*model.Acquirer
		outputErr error
	}

	tests := []struct {
		name                 string
		reqProjectID         uint64
		reqTransactionTypeID uint64
		getAllPaymentTypes   getAllPaymentTypesOp
		getAcquirersByID     getAcquirersByIDOp
		want                 []*model.Acquirer
		wantErr              error
	}{
		{
			name:                 "error when getting all by payment types",
			reqTransactionTypeID: 1,
			reqProjectID:         24,
			getAllPaymentTypes: getAllPaymentTypesOp{
				inputProjectID:         24,
				inputTransactionTypeID: 1,
				output:                 nil,
				outputErr:              errors.New("some error error"),
			},
			wantErr: errors.New("some error error"),
			want:    nil,
		},
		{
			name:                 "error when getting acquirers by id",
			reqTransactionTypeID: 1,
			reqProjectID:         24,
			getAllPaymentTypes: getAllPaymentTypesOp{
				inputProjectID:         24,
				inputTransactionTypeID: 1,
				output: []*model.Terminal{
					{
						ID:              22,
						AcquirerID:      1,
						Status:          1,
						EncryptedConfig: "AAAAAAAAAAAAAAAAAAAAACmWAaXWruscAToiSj3tmeWGkgJixFU+f2haCQGB7+rXa+cOfXkDHOxJ37G17/HjmaYtUVGwfmYDZJDQVb4UQprd6erkboic2rT5U5da8HBs",
					},
					{
						ID:              23,
						AcquirerID:      2,
						Status:          1,
						EncryptedConfig: "AAAAAAAAAAAAAAAAAAAAACgM70S19V48AFL7pceC+v3oYAvD2nAW5RCD160Vo6MOy7L9nBXQyCvPAt9zgtpBTrAHO53qtWAfdZCmADfo2gaAbMBvaHGQtSfHEEgauzsZKBLhv1nTGsjTqq9fWETzZn2tH/PxcKkc1eeNo530Rox2x05F3OwmY2VHr1dTLB+D",
					},
				},
				outputErr: nil,
			},
			getAcquirersByID: getAcquirersByIDOp{
				isCalled:  true,
				output:    nil,
				outputErr: errors.New("some error"),
				input:     []uint64{1, 2},
			},
			wantErr: errors.New("some error"),
			want:    nil,
		},
		{
			name:                 "success",
			reqTransactionTypeID: 1,
			reqProjectID:         24,
			getAllPaymentTypes: getAllPaymentTypesOp{
				inputProjectID:         24,
				inputTransactionTypeID: 1,
				output: []*model.Terminal{
					{
						ID:              22,
						AcquirerID:      1,
						Status:          1,
						EncryptedConfig: "AAAAAAAAAAAAAAAAAAAAACmWAaXWruscAToiSj3tmeWGkgJixFU+f2haCQGB7+rXa+cOfXkDHOxJ37G17/HjmaYtUVGwfmYDZJDQVb4UQprd6erkboic2rT5U5da8HBs",
					},
					{
						ID:              23,
						AcquirerID:      2,
						Status:          1,
						EncryptedConfig: "AAAAAAAAAAAAAAAAAAAAACgM70S19V48AFL7pceC+v3oYAvD2nAW5RCD160Vo6MOy7L9nBXQyCvPAt9zgtpBTrAHO53qtWAfdZCmADfo2gaAbMBvaHGQtSfHEEgauzsZKBLhv1nTGsjTqq9fWETzZn2tH/PxcKkc1eeNo530Rox2x05F3OwmY2VHr1dTLB+D",
					},
				},
				outputErr: nil,
			},
			getAcquirersByID: getAcquirersByIDOp{
				isCalled: true,
				output: []*model.Acquirer{
					{
						ID:        1,
						Code:      "bcc",
						Name:      "bank center credit",
						BankID:    74,
						CountryID: 22,
					},
					{
						ID:        2,
						Code:      "freedom",
						Name:      "freedom",
						BankID:    75,
						CountryID: 22,
					},
				},
				outputErr: nil,
				input:     []uint64{1, 2},
			},
			wantErr: nil,
			want: []*model.Acquirer{
				{
					ID:        1,
					Code:      "bcc",
					Name:      "bank center credit",
					BankID:    74,
					CountryID: 22,
				},
				{
					ID:        2,
					Code:      "freedom",
					Name:      "freedom",
					BankID:    75,
					CountryID: 22,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()

			ctx, cancelFunc := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancelFunc, nil, nil, nil)

			terminalDBMock := databasemocks.NewMockTerminaler(ctrl)
			acquirerDBMock := databasemocks.NewMockAcquirer(ctrl)

			terminalDBMock.EXPECT().GetAllByPaymentType(
				gomock.Any(),
				tt.getAllPaymentTypes.inputProjectID,
				tt.getAllPaymentTypes.inputTransactionTypeID,
			).Return(tt.getAllPaymentTypes.output, tt.getAllPaymentTypes.outputErr).Times(1)

			if tt.getAcquirersByID.isCalled {
				acquirerDBMock.EXPECT().GetAcquirersById(
					gomock.Any(),
					tt.getAcquirersByID.input,
				).Return(tt.getAcquirersByID.output, tt.getAcquirersByID.outputErr).Times(1)
			}

			projectService := NewProjectService(nil, acquirerDBMock, terminalDBMock, nil)

			resp, err := projectService.GetAcquirersByPaymentType(context.Background(), tt.reqProjectID, tt.reqTransactionTypeID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}
