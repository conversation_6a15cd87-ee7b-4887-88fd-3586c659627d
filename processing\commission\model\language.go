package model

type ContextKeyType string

const (
	LanguageKey    ContextKeyType = "language"
	NameKey        string         = "name"
	DescriptionKey string         = "description"
)

func (ckt ContextKeyType) ToString() string {
	return string(ckt)
}

// iterateOverMap проходится по конфигу и определяет наличие нужных ему ключей
// `language` и `name`. Ключ `language` используется для определения наличия
// кода языка из запроса. Ключ `name` ищет сам перевод, а затем возвращает его, если есть
func iterateOverMap(
	configs []map[string]string,
	languageKey string,
	ctxLanguage string,
) (string, string) {
	var (
		nameValue, descriptionValue string
	)

	for _, m := range configs {
		languageCode, isLanguageKeyFound := m[languageKey]
		if isLanguageKeyFound {
			if languageCode == ctxLanguage {
				nameValue = m[NameKey]
				descriptionValue = m[DescriptionKey]
			}
		}
	}

	return nameValue, descriptionValue
}
