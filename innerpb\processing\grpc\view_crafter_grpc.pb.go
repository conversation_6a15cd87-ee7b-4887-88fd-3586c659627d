// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/view_crafter.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ViewCrafter_GetProjectFormInfoV1_FullMethodName = "/processing.view_crafter.view_crafter.ViewCrafter/GetProjectFormInfoV1"
)

// ViewCrafterClient is the client API for ViewCrafter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ViewCrafterClient interface {
	GetProjectFormInfoV1(ctx context.Context, in *GetProjectFormInfoReqV1, opts ...grpc.CallOption) (*GetProjectFormInfoResV1, error)
}

type viewCrafterClient struct {
	cc grpc.ClientConnInterface
}

func NewViewCrafterClient(cc grpc.ClientConnInterface) ViewCrafterClient {
	return &viewCrafterClient{cc}
}

func (c *viewCrafterClient) GetProjectFormInfoV1(ctx context.Context, in *GetProjectFormInfoReqV1, opts ...grpc.CallOption) (*GetProjectFormInfoResV1, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetProjectFormInfoResV1)
	err := c.cc.Invoke(ctx, ViewCrafter_GetProjectFormInfoV1_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ViewCrafterServer is the server API for ViewCrafter service.
// All implementations must embed UnimplementedViewCrafterServer
// for forward compatibility.
type ViewCrafterServer interface {
	GetProjectFormInfoV1(context.Context, *GetProjectFormInfoReqV1) (*GetProjectFormInfoResV1, error)
	mustEmbedUnimplementedViewCrafterServer()
}

// UnimplementedViewCrafterServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedViewCrafterServer struct{}

func (UnimplementedViewCrafterServer) GetProjectFormInfoV1(context.Context, *GetProjectFormInfoReqV1) (*GetProjectFormInfoResV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProjectFormInfoV1 not implemented")
}
func (UnimplementedViewCrafterServer) mustEmbedUnimplementedViewCrafterServer() {}
func (UnimplementedViewCrafterServer) testEmbeddedByValue()                     {}

// UnsafeViewCrafterServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ViewCrafterServer will
// result in compilation errors.
type UnsafeViewCrafterServer interface {
	mustEmbedUnimplementedViewCrafterServer()
}

func RegisterViewCrafterServer(s grpc.ServiceRegistrar, srv ViewCrafterServer) {
	// If the following call pancis, it indicates UnimplementedViewCrafterServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ViewCrafter_ServiceDesc, srv)
}

func _ViewCrafter_GetProjectFormInfoV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProjectFormInfoReqV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ViewCrafterServer).GetProjectFormInfoV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ViewCrafter_GetProjectFormInfoV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ViewCrafterServer).GetProjectFormInfoV1(ctx, req.(*GetProjectFormInfoReqV1))
	}
	return interceptor(ctx, in, info, handler)
}

// ViewCrafter_ServiceDesc is the grpc.ServiceDesc for ViewCrafter service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ViewCrafter_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.view_crafter.view_crafter.ViewCrafter",
	HandlerType: (*ViewCrafterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetProjectFormInfoV1",
			Handler:    _ViewCrafter_GetProjectFormInfoV1_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/view_crafter.proto",
}
