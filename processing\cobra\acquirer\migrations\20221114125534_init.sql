-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS "acquirer"."countries"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "id" SERIAL PRIMARY KEY,
    "name" VARCHAR
(
    255
) NOT NULL
    );

CREATE TABLE IF NOT EXISTS "acquirer"."currencies"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "id" SERIAL PRIMARY KEY,
    "name" VARCHAR
(
    255
) NOT NULL,
    "letter_code" VARCHAR
(
    255
) NOT NULL,
    "digital_code" VARCHAR
(
    255
) NOT NULL
    );

CREATE TABLE IF NOT EXISTS "acquirer"."banks"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "id" BIGSERIAL PRIMARY KEY,
    "name" VARCHAR
(
    255
) NOT NULL,
    "bik" VARCHAR
(
    255
),
    "swift" VARCHAR
(
    255
)
    );

CREATE INDEX IF NOT EXISTS idx_acquirer_bank_id ON "acquirer".banks (id);

CREATE TABLE IF NOT EXISTS "acquirer"."ips"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "id" SERIAL PRIMARY KEY,
    "name" VARCHAR
(
    255
) NOT NULL,
    UNIQUE
(
    "name"
)
    );

CREATE INDEX IF NOT EXISTS idx_acquirer_ips_id ON "acquirer"."ips" (id);

create table if not exists acquirer.bank_bins
(
    created_at
    timestamp
    default
    now
(
),
    updated_at timestamp default now
(
),
    id bigserial primary key,
    bank_id bigint
    constraint bank_bins_bank_id_fk references acquirer.banks,
    bin varchar
(
    255
) not null UNIQUE,
    country_id bigint
    constraint bank_bins_countries_id_fk references acquirer.countries,
    ips_id bigint
    constraint bank_bins_ips_id_fk references acquirer.ips
    );


CREATE INDEX IF NOT EXISTS idx_acquirer_bin_id ON "acquirer"."bank_bins" (id);
CREATE INDEX IF NOT EXISTS idx_acquirer_bin_bank_id ON "acquirer"."bank_bins" (bank_id);

CREATE TABLE IF NOT EXISTS "acquirer"."acquirers"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "id" SERIAL PRIMARY KEY,
    "code" VARCHAR
(
    255
) UNIQUE NOT NULL,
    "name" VARCHAR
(
    255
),
    "bank_id" BIGINT NOT NULL REFERENCES "acquirer"."banks",
    "contract" VARCHAR
(
    255
) NOT NULL,
    "country_id" INTEGER NOT NULL REFERENCES "acquirer"."countries",
    "description" VARCHAR
(
    255
),
    "is_active" BOOLEAN DEFAULT TRUE NOT NULL
    );

CREATE INDEX IF NOT EXISTS idx_acquirer_acquirer_id ON "acquirer"."acquirers" (id);
CREATE INDEX IF NOT EXISTS idx_acquirer_contract ON "acquirer"."acquirers" (contract);
COMMENT
ON COLUMN "acquirer".acquirers.contract IS 'Договор подписанный с эквайером.
    Может являтся номером документа или его названием.';

CREATE TABLE IF NOT EXISTS "acquirer"."terminals"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
) NOT NULL,
    "updated_at" TIMESTAMP DEFAULT NOW
(
) NOT NULL,
    "id" BIGSERIAL PRIMARY KEY,
    "acquirer_id" INTEGER NOT NULL REFERENCES "acquirer"."acquirers",
    "project_id" INTEGER NOT NULL,
    "transaction_type_id" INTEGER NOT NULL,
    "status" INTEGER DEFAULT 2,
    "config" JSONB NOT NULL
    );

CREATE INDEX IF NOT EXISTS idx_acquirer_terminal_id ON "acquirer"."terminals" (id);
CREATE INDEX IF NOT EXISTS idx_acquirer_terminal_pay_type ON "acquirer"."terminals" (transaction_type_id);
CREATE INDEX IF NOT EXISTS idx_acquirer_terminal_project_id ON "acquirer"."terminals" (project_id);
CREATE INDEX IF NOT EXISTS idx_acquirer_terminal_acquirer_id ON "acquirer"."terminals" (acquirer_id);
COMMENT
ON COLUMN "acquirer"."terminals".status IS '1 - Терминал включен, 2 - Терминал выключен, 3 - Global off';

CREATE TABLE IF NOT EXISTS "acquirer"."rules"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "id" BIGSERIAL PRIMARY KEY,
    "project_id" BIGINT NOT NULL,
    "transaction_type_id" SMALLINT NOT NULL,
    "ips_id" INTEGER REFERENCES "acquirer"."ips",
    "issuer_id" BIGINT REFERENCES "acquirer"."banks",
    "country_id" INTEGER REFERENCES "acquirer"."countries",
    "amount_from" FLOAT,
    "amount_to" FLOAT,
    "weight" SMALLINT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_base" BOOLEAN NOT NULL DEFAULT false
    );

CREATE INDEX IF NOT EXISTS idx_acquirer_rules_id ON "acquirer"."rules" (id);
CREATE INDEX IF NOT EXISTS idx_acquirer_rules_project_id ON "acquirer"."rules" (project_id);
CREATE INDEX IF NOT EXISTS idx_acquirer_rules_country_id ON "acquirer"."rules" (country_id);
CREATE INDEX IF NOT EXISTS idx_acquirer_rules_ips ON "acquirer"."rules" (ips_id);
CREATE INDEX IF NOT EXISTS idx_acquirer_rules_issuer_id ON "acquirer"."rules" (issuer_id);
CREATE INDEX IF NOT EXISTS idx_acquirer_rules_transaction_type_id ON "acquirer"."rules" (transaction_type_id);

COMMENT
ON TABLE acquirer.rules IS
    'Таблица предназначена для описывания правил.
    Далее эти правила будут использоваться для балансировки между эквайерами';
COMMENT
ON COLUMN acquirer.rules.weight IS
    'Вес правил. Чем больше вес тем приоритетнее, базовая правила всегда будет весом 1';
COMMENT
ON COLUMN acquirer.rules.amount_from IS
    'Минимум сумма для этого правила. Для платежей со суммой больше этого значения включительно';
COMMENT
ON COLUMN acquirer.rules.amount_from IS
    'Минимум сумма для этого правила. Для платежей со суммой меньше этого значения включительно';

CREATE TABLE IF NOT EXISTS "acquirer"."rule_percentage"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "id" SERIAL PRIMARY KEY,
    "rule_id" INTEGER NOT NULL REFERENCES "acquirer"."rules",
    "acquirer_id" INTEGER NOT NULL REFERENCES "acquirer"."acquirers",
    "percentage" SMALLINT NOT NULL
    );

CREATE INDEX IF NOT EXISTS idx_acquirer_rule_percentage_id ON "acquirer"."rule_percentage" (id);
CREATE INDEX IF NOT EXISTS idx_acquirer_rule_percentage_acq_id ON "acquirer"."rule_percentage" (acquirer_id);
ALTER TABLE acquirer.rule_percentage
    ADD CONSTRAINT unique_rules_for_acquirers UNIQUE (rule_id, acquirer_id);
COMMENT
ON TABLE acquirer.rule_percentage IS
    'Таблица служит для выставления процентажа между правилами для эквайеров';

CREATE TABLE IF NOT EXISTS "acquirer"."bank_services"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "id" SERIAL PRIMARY KEY,
    "name" VARCHAR
(
    255
) NOT NULL
    );

CREATE INDEX IF NOT EXISTS idx_acquirer_bank_service_id ON "acquirer"."bank_services" (id);

CREATE TABLE IF NOT EXISTS "acquirer"."ips_countries"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "ips_id" INTEGER NOT NULL REFERENCES "acquirer"."ips",
    "country_id" INTEGER NOT NULL REFERENCES "acquirer"."countries",
    PRIMARY KEY
(
    ips_id,
    country_id
)
    );

CREATE INDEX IF NOT EXISTS idx_acquirer_ips_countries_ips_id ON "acquirer"."ips" (id);
CREATE INDEX IF NOT EXISTS idx_acquirer_ips_countries_country_id ON "acquirer"."countries" (id);


CREATE TABLE IF NOT EXISTS "acquirer"."country_currencies"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "country_id" INTEGER NOT NULL REFERENCES "acquirer"."countries",
    "currency_id" INTEGER NOT NULL REFERENCES "acquirer"."currencies",
    PRIMARY KEY
(
    country_id,
    currency_id
)
    );
CREATE INDEX IF NOT EXISTS idx_acquirer_country_currencies_country_id ON "acquirer"."countries" (id);
CREATE INDEX IF NOT EXISTS idx_acquirer_country_currencies_currency_id ON "acquirer"."currencies" (id);

CREATE TABLE IF NOT EXISTS "acquirer"."acquirer_currencies"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "acquirer_id" BIGINT NOT NULL REFERENCES "acquirer"."acquirers",
    "currency_id" INTEGER NOT NULL REFERENCES "acquirer"."currencies",
    PRIMARY KEY
(
    acquirer_id,
    currency_id
)
    );
CREATE INDEX IF NOT EXISTS idx_acquirer_country_currencies_acquirer_id ON "acquirer"."acquirers" (id);
CREATE INDEX IF NOT EXISTS idx_acquirer_country_currencies_currency_id ON "acquirer"."currencies" (id);

CREATE TABLE IF NOT EXISTS "acquirer"."bank_limits"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "id" SERIAL PRIMARY KEY,
    "other_limit" VARCHAR
(
    255
),
    "year" INTEGER,
    "month" INTEGER,
    "day" INTEGER,
    "week" INTEGER,
    "transaction" INTEGER,
    "bank_service_id" INTEGER NOT NULL REFERENCES "acquirer"."bank_services",
    "acquirer_id" INTEGER NOT NULL REFERENCES "acquirer"."acquirers"
    );

CREATE INDEX IF NOT EXISTS idx_acquirer_bank_limits_bank_service_id ON "acquirer"."bank_services" (id);
CREATE INDEX IF NOT EXISTS idx_acquirer_bank_limits_acquirer_id ON "acquirer"."acquirers" (id);

CREATE TABLE IF NOT EXISTS "acquirer"."ips_digit_codes"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "id" SERIAL PRIMARY KEY,
    "first_digit" INT NOT NULL,
    "second_digit" INT DEFAULT NULL,
    "ips_id" BIGINT NOT NULL REFERENCES "acquirer"."ips"
(
    id
)
    );

CREATE INDEX IF NOT EXISTS idx_acquirer_ips_digit_codes_id ON "acquirer"."ips_digit_codes" (id);

CREATE TABLE IF NOT EXISTS "acquirer"."country_banks"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "country_id" INTEGER NOT NULL REFERENCES "acquirer"."countries",
    "bank_id" INTEGER NOT NULL REFERENCES "acquirer"."banks",
    PRIMARY KEY
(
    country_id,
    bank_id
)
    );
CREATE INDEX IF NOT EXISTS idx_acquirer_country_banks_country_id ON "acquirer"."countries" (id);
CREATE INDEX IF NOT EXISTS idx_acquirer_country_banks_bank_id ON "acquirer"."banks" (id);

CREATE TABLE IF NOT EXISTS "acquirer"."acquirer_percentages"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
),
    "updated_at" TIMESTAMP DEFAULT NOW
(
),
    "id" SERIAL PRIMARY KEY,
    "ips_id" BIGINT NOT NULL REFERENCES "acquirer"."ips",
    "mcc_id" BIGINT NOT NULL,
    "acquirer_id" BIGINT NOT NULL REFERENCES "acquirer"."acquirers",
    "issuer_id" BIGINT NOT NULL REFERENCES "acquirer"."banks",
    "issuer_country_id" BIGINT NOT NULL REFERENCES "acquirer"."countries",
    "percentage" FLOAT,
    "min" FLOAT,
    "max" FLOAT,
    "fix" INTEGER
    );

CREATE INDEX IF NOT EXISTS idx_acquirer_percentage_issuer_country_id ON "acquirer"."countries" (id);
CREATE INDEX IF NOT EXISTS idx_acquirer_percentage_issuer_id ON "acquirer"."banks" (id);
CREATE INDEX IF NOT EXISTS idx_acquirer_percentage_acquirer_id ON "acquirer"."acquirers" (id);
CREATE INDEX IF NOT EXISTS idx_acquirer_percentage_ips_id ON "acquirer"."ips" (id);

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- DROP TABLE "acquirer"."banks";
-- DROP TABLE "acquirer"."bins";
-- DROP TABLE "acquirer"."acquirers";
-- DROP TABLE "acquirer"."rule_percentage";
-- DROP TABLE "acquirer"."bank_services";
-- DROP TABLE "acquirer"."ips";
-- DROP TABLE "acquirer"."countries";
-- DROP TABLE "acquirer"."ips_countries";
-- DROP TABLE "acquirer"."currencies";
-- DROP TABLE "acquirer"."country_currencies";
-- DROP TABLE "acquirer"."acquirer_cards";
-- DROP TABLE "acquirer"."ips_digit_codes";
-- +goose StatementEnd
