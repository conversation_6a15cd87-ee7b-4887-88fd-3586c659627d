// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	fmt "fmt"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func file_inner_processing_grpc_transfer_automatic_proto_message_EmptyToZap(
	label string,
	in *emptypb.Empty,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
	)
}

func file_inner_processing_grpc_transfer_automatic_proto_message_CreateTransferByRulesRequestToZap(
	label string,
	in *CreateTransferByRulesRequest,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		file_inner_processing_grpc_transfer_automatic_proto_enum_EnumFrequencyTypeToZap("Frequency", in.GetFrequency()),
	)
}

func file_inner_processing_grpc_transfer_automatic_proto_enum_EnumFrequencyTypeToZap(
	label string,
	in EnumFrequencyType,
) zap.Field {
	str, ok := EnumFrequencyType_name[int32(in)]
	if !ok {
		str = fmt.Sprintf("unknown EnumFrequencyType value %d", in)
	}

	return zap.Dict(
		label,
		zap.Any("value", EnumFrequencyType(in)),
		zap.Any("name", str),
	)
}

func file_inner_processing_grpc_transfer_automatic_proto_message_FrequencyRefToZap(
	label string,
	in *FrequencyRef,
) zap.Field {
	if in == nil {
		return zap.Reflect(label, in)
	}

	return zap.Dict(
		label,
		zap.Any("Code", in.GetCode()),
		zap.Any("Name", in.GetName()),
	)
}

var _ TransferAutomaticServer = (*loggedTransferAutomaticServer)(nil)

func NewLoggedTransferAutomaticServer(srv TransferAutomaticServer) TransferAutomaticServer {
	return &loggedTransferAutomaticServer{srv: srv}
}

type loggedTransferAutomaticServer struct {
	UnimplementedTransferAutomaticServer

	srv TransferAutomaticServer
}

func (s *loggedTransferAutomaticServer) StartCreateTransferByRulesWorker(
	ctx context.Context,
	request *CreateTransferByRulesRequest,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransferAutomaticServer_StartCreateTransferByRulesWorker")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transfer_automatic_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transfer_automatic_proto_message_CreateTransferByRulesRequestToZap(label+"request", request),
	)

	response, err = s.srv.StartCreateTransferByRulesWorker(ctx, request)

	return
}

var _ TransferAutomaticClient = (*loggedTransferAutomaticClient)(nil)

func NewLoggedTransferAutomaticClient(client TransferAutomaticClient) TransferAutomaticClient {
	return &loggedTransferAutomaticClient{client: client}
}

type loggedTransferAutomaticClient struct {
	client TransferAutomaticClient
}

func (s *loggedTransferAutomaticClient) StartCreateTransferByRulesWorker(
	ctx context.Context,
	request *CreateTransferByRulesRequest,
	opts ...grpc.CallOption,
) (
	response *emptypb.Empty,
	err error,
) {
	label := cntx.Begin(ctx, "TransferAutomaticClient_StartCreateTransferByRulesWorker")
	defer cntx.End(ctx, label)

	defer func() {
		if err != nil {
			ctxzap.AddFields(ctx, zap.Any(label+"error", err))
			return
		}

		ctxzap.AddFields(
			ctx,
			file_inner_processing_grpc_transfer_automatic_proto_message_EmptyToZap(label+"response", response),
		)
	}()

	ctxzap.AddFields(
		ctx,
		file_inner_processing_grpc_transfer_automatic_proto_message_CreateTransferByRulesRequestToZap(label+"request", request),
	)

	response, err = s.client.StartCreateTransferByRulesWorker(ctx, request, opts...)

	return
}
