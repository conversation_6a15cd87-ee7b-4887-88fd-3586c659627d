// Code generated by MockGen. DO NOT EDIT.
// Source: transfer_automatic_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockTransferAutomaticClient is a mock of TransferAutomaticClient interface.
type MockTransferAutomaticClient struct {
	ctrl     *gomock.Controller
	recorder *MockTransferAutomaticClientMockRecorder
}

// MockTransferAutomaticClientMockRecorder is the mock recorder for MockTransferAutomaticClient.
type MockTransferAutomaticClientMockRecorder struct {
	mock *MockTransferAutomaticClient
}

// NewMockTransferAutomaticClient creates a new mock instance.
func NewMockTransferAutomaticClient(ctrl *gomock.Controller) *MockTransferAutomaticClient {
	mock := &MockTransferAutomaticClient{ctrl: ctrl}
	mock.recorder = &MockTransferAutomaticClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransferAutomaticClient) EXPECT() *MockTransferAutomaticClientMockRecorder {
	return m.recorder
}

// StartCreateTransferByRulesWorker mocks base method.
func (m *MockTransferAutomaticClient) StartCreateTransferByRulesWorker(ctx context.Context, in *grpc.CreateTransferByRulesRequest, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartCreateTransferByRulesWorker", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartCreateTransferByRulesWorker indicates an expected call of StartCreateTransferByRulesWorker.
func (mr *MockTransferAutomaticClientMockRecorder) StartCreateTransferByRulesWorker(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartCreateTransferByRulesWorker", reflect.TypeOf((*MockTransferAutomaticClient)(nil).StartCreateTransferByRulesWorker), varargs...)
}

// MockTransferAutomaticServer is a mock of TransferAutomaticServer interface.
type MockTransferAutomaticServer struct {
	ctrl     *gomock.Controller
	recorder *MockTransferAutomaticServerMockRecorder
}

// MockTransferAutomaticServerMockRecorder is the mock recorder for MockTransferAutomaticServer.
type MockTransferAutomaticServerMockRecorder struct {
	mock *MockTransferAutomaticServer
}

// NewMockTransferAutomaticServer creates a new mock instance.
func NewMockTransferAutomaticServer(ctrl *gomock.Controller) *MockTransferAutomaticServer {
	mock := &MockTransferAutomaticServer{ctrl: ctrl}
	mock.recorder = &MockTransferAutomaticServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransferAutomaticServer) EXPECT() *MockTransferAutomaticServerMockRecorder {
	return m.recorder
}

// StartCreateTransferByRulesWorker mocks base method.
func (m *MockTransferAutomaticServer) StartCreateTransferByRulesWorker(arg0 context.Context, arg1 *grpc.CreateTransferByRulesRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartCreateTransferByRulesWorker", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartCreateTransferByRulesWorker indicates an expected call of StartCreateTransferByRulesWorker.
func (mr *MockTransferAutomaticServerMockRecorder) StartCreateTransferByRulesWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartCreateTransferByRulesWorker", reflect.TypeOf((*MockTransferAutomaticServer)(nil).StartCreateTransferByRulesWorker), arg0, arg1)
}

// mustEmbedUnimplementedTransferAutomaticServer mocks base method.
func (m *MockTransferAutomaticServer) mustEmbedUnimplementedTransferAutomaticServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTransferAutomaticServer")
}

// mustEmbedUnimplementedTransferAutomaticServer indicates an expected call of mustEmbedUnimplementedTransferAutomaticServer.
func (mr *MockTransferAutomaticServerMockRecorder) mustEmbedUnimplementedTransferAutomaticServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTransferAutomaticServer", reflect.TypeOf((*MockTransferAutomaticServer)(nil).mustEmbedUnimplementedTransferAutomaticServer))
}

// MockUnsafeTransferAutomaticServer is a mock of UnsafeTransferAutomaticServer interface.
type MockUnsafeTransferAutomaticServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTransferAutomaticServerMockRecorder
}

// MockUnsafeTransferAutomaticServerMockRecorder is the mock recorder for MockUnsafeTransferAutomaticServer.
type MockUnsafeTransferAutomaticServerMockRecorder struct {
	mock *MockUnsafeTransferAutomaticServer
}

// NewMockUnsafeTransferAutomaticServer creates a new mock instance.
func NewMockUnsafeTransferAutomaticServer(ctrl *gomock.Controller) *MockUnsafeTransferAutomaticServer {
	mock := &MockUnsafeTransferAutomaticServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTransferAutomaticServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTransferAutomaticServer) EXPECT() *MockUnsafeTransferAutomaticServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTransferAutomaticServer mocks base method.
func (m *MockUnsafeTransferAutomaticServer) mustEmbedUnimplementedTransferAutomaticServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTransferAutomaticServer")
}

// mustEmbedUnimplementedTransferAutomaticServer indicates an expected call of mustEmbedUnimplementedTransferAutomaticServer.
func (mr *MockUnsafeTransferAutomaticServerMockRecorder) mustEmbedUnimplementedTransferAutomaticServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTransferAutomaticServer", reflect.TypeOf((*MockUnsafeTransferAutomaticServer)(nil).mustEmbedUnimplementedTransferAutomaticServer))
}
