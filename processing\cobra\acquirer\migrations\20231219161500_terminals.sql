-- +goose Up
-- +goose StatementBegin

CREATE TABLE IF NOT EXISTS "acquirer"."terminal_projects"
(
    "created_at"
    TIMESTAMP
    DEFAULT
    NOW
(
) NOT NULL,
    "updated_at" TIMESTAMP DEFAULT NOW
(
) NOT NULL,
    "id" BIGSERIAL PRIMARY KEY,
    "terminal_id" BIGINT NOT NULL REFERENCES "acquirer"."terminals",
    "project_id" INTEGER NOT NULL,
    "transaction_type_id" INTEGER NOT NULL,
    "is_active" BOOLEAN DEFAULT FALSE NOT NULL
    );

CREATE INDEX IF NOT EXISTS idx_acquirer_terminal_project_pay_type ON "acquirer"."terminal_projects" (transaction_type_id);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- ALTER TABLE "acquirer"."terminals" ADD COLUMN "transaction_type_id" INTEGER;
-- ALTER TABLE "acquirer"."terminals" ADD COLUMN "project_id" INTEGER;
-- DROP TABLE "acquirer"."terminal_projects";
-- +goose StatementEnd
