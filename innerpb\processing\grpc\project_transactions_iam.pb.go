// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

func NewIamProjectTransactionsServer(
	srv ProjectTransactionsServer,
) ProjectTransactionsServer {
	return &iamProjectTransactionsServer{
		srv: srv,
	}
}

var _ ProjectTransactionsServer = (*iamProjectTransactionsServer)(nil)

type iamProjectTransactionsServer struct {
	UnimplementedProjectTransactionsServer

	srv ProjectTransactionsServer
}

func (s *iamProjectTransactionsServer) CheckAmountLimit(
	ctx context.Context,
	req *CheckAmountLimitRequestV1,
) (
	*CheckAmountLimitResponseV1,
	error,
) {
	return s.srv.CheckAmountLimit(ctx, req)
}

func (s *iamProjectTransactionsServer) CheckAttemptsWithinTimeout(
	ctx context.Context,
	req *CheckAttemptsWithinTimeoutRequestV1,
) (
	*CheckAttemptsWithinTimeoutResponseV1,
	error,
) {
	return s.srv.CheckAttemptsWithinTimeout(ctx, req)
}

func (s *iamProjectTransactionsServer) GetTransactionLimit(
	ctx context.Context,
	req *GetTransactionLimitRequestV1,
) (
	*GetTransactionLimitResponseV1,
	error,
) {
	return s.srv.GetTransactionLimit(ctx, req)
}

func NewIamProjectTransactionsClient(
	client ProjectTransactionsClient,
) ProjectTransactionsClient {
	return &iamProjectTransactionsClient{
		client: client,
	}
}

type iamProjectTransactionsClient struct {
	client ProjectTransactionsClient
}

func (s *iamProjectTransactionsClient) CheckAmountLimit(
	ctx context.Context,
	req *CheckAmountLimitRequestV1,
	opts ...grpc.CallOption,
) (
	*CheckAmountLimitResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CheckAmountLimit(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamProjectTransactionsClient) CheckAttemptsWithinTimeout(
	ctx context.Context,
	req *CheckAttemptsWithinTimeoutRequestV1,
	opts ...grpc.CallOption,
) (
	*CheckAttemptsWithinTimeoutResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.CheckAttemptsWithinTimeout(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamProjectTransactionsClient) GetTransactionLimit(
	ctx context.Context,
	req *GetTransactionLimitRequestV1,
	opts ...grpc.CallOption,
) (
	*GetTransactionLimitResponseV1,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetTransactionLimit(metadata.NewOutgoingContext(ctx, md), req)
}
