// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: inner/processing/grpc/splitting.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Splitting_CalculatePaymentSplitTax_FullMethodName = "/processing.splitting.splitting.Splitting/CalculatePaymentSplitTax"
)

// SplittingClient is the client API for Splitting service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SplittingClient interface {
	CalculatePaymentSplitTax(ctx context.Context, in *CalculatePaymentSplitTaxRequest, opts ...grpc.CallOption) (*CalculatePaymentSplitTaxResponse, error)
}

type splittingClient struct {
	cc grpc.ClientConnInterface
}

func NewSplittingClient(cc grpc.ClientConnInterface) SplittingClient {
	return &splittingClient{cc}
}

func (c *splittingClient) CalculatePaymentSplitTax(ctx context.Context, in *CalculatePaymentSplitTaxRequest, opts ...grpc.CallOption) (*CalculatePaymentSplitTaxResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CalculatePaymentSplitTaxResponse)
	err := c.cc.Invoke(ctx, Splitting_CalculatePaymentSplitTax_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SplittingServer is the server API for Splitting service.
// All implementations must embed UnimplementedSplittingServer
// for forward compatibility.
type SplittingServer interface {
	CalculatePaymentSplitTax(context.Context, *CalculatePaymentSplitTaxRequest) (*CalculatePaymentSplitTaxResponse, error)
	mustEmbedUnimplementedSplittingServer()
}

// UnimplementedSplittingServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSplittingServer struct{}

func (UnimplementedSplittingServer) CalculatePaymentSplitTax(context.Context, *CalculatePaymentSplitTaxRequest) (*CalculatePaymentSplitTaxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculatePaymentSplitTax not implemented")
}
func (UnimplementedSplittingServer) mustEmbedUnimplementedSplittingServer() {}
func (UnimplementedSplittingServer) testEmbeddedByValue()                   {}

// UnsafeSplittingServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SplittingServer will
// result in compilation errors.
type UnsafeSplittingServer interface {
	mustEmbedUnimplementedSplittingServer()
}

func RegisterSplittingServer(s grpc.ServiceRegistrar, srv SplittingServer) {
	// If the following call pancis, it indicates UnimplementedSplittingServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Splitting_ServiceDesc, srv)
}

func _Splitting_CalculatePaymentSplitTax_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculatePaymentSplitTaxRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplittingServer).CalculatePaymentSplitTax(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Splitting_CalculatePaymentSplitTax_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplittingServer).CalculatePaymentSplitTax(ctx, req.(*CalculatePaymentSplitTaxRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Splitting_ServiceDesc is the grpc.ServiceDesc for Splitting service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Splitting_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "processing.splitting.splitting.Splitting",
	HandlerType: (*SplittingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CalculatePaymentSplitTax",
			Handler:    _Splitting_CalculatePaymentSplitTax_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "inner/processing/grpc/splitting.proto",
}
