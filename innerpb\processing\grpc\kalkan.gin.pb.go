// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	dog "git.local/sensitive/sdk/dog"
	gin "github.com/gin-gonic/gin"
	metadata "google.golang.org/grpc/metadata"
	http "net/http"
)

func RegisterGinKalkanRouterGroup(
	in *gin.RouterGroup,
	handler func(func(c *gin.Context) error) gin.HandlerFunc,
) error {
	service, err := NewGinKalkanService()
	if err != nil {
		return err
	}

	routerGroup := in.Group("processing.kalkan.kalkan.Kalkan")
	routerGroup.PUT("/MakeSignatureV1", handler(service.MakeSignatureV1))
	return nil
}

func NewGinKalkanService() (GinKalkanServer, error) {
	client, err := NewPreparedKalkanClient()
	if err != nil {
		return nil, err
	}

	return &ginKalkanServer{
		client: NewLoggedKalkanClient(
			NewIamKalkanClient(client),
		),
	}, nil
}

//go:generate mockgen -destination=./grpcmock/kalkan.gin.pb.go -package=grpcmock -source=kalkan.gin.pb.go GinKalkanServer
type GinKalkanServer interface {
	MakeSignatureV1(c *gin.Context) error
}

var _ GinKalkanServer = (*ginKalkanServer)(nil)

type ginKalkanServer struct {
	client KalkanClient
}

type Kalkan_MakeSignatureV1_Success struct {
	// Status is the status of the response. Set true
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status. Set 0
	StatusCode int `json:"status_code"`
	// Result is the result of the request
	Result *MakeSignatureResponseV1 `json:"result"`
}

type Kalkan_MakeSignatureV1_Failure struct {
	// Status is the status of the response. Set false
	Status bool `json:"status"`
	// Message is a human-readable explanation of the status
	Message string `json:"message"`
	// StatusCode is the GoErr status
	StatusCode int `json:"status_code"`
}

// MakeSignatureV1
// @Summary MakeSignatureV1
// @Security bearerAuth
// @ID Kalkan_MakeSignatureV1
// @Accept json
// @Param request body MakeSignatureRequestV1 true "MakeSignatureRequestV1"
// @Success 200 {object} Kalkan_MakeSignatureV1_Success
// @Failure 401 {object} Kalkan_MakeSignatureV1_Failure "goerrpkg.ErrorTypeAuthorization"
// @Failure 404 {object} Kalkan_MakeSignatureV1_Failure "goerrpkg.ErrorTypeNotFound"
// @Failure 400 {object} Kalkan_MakeSignatureV1_Failure "goerrpkg.ErrorTypeBadRequest"
// @Failure 500 {object} Kalkan_MakeSignatureV1_Failure "goerrpkg.ErrorTypeUnexpectedError"
// @Failure 400 {object} Kalkan_MakeSignatureV1_Failure "gin.ErrorTypeBind"
// @Failure 500 {object} Kalkan_MakeSignatureV1_Failure "Undefined error"
// @Produce json
// @Router /processing.kalkan.kalkan.Kalkan/MakeSignatureV1 [put]
// @tags Kalkan
func (s *ginKalkanServer) MakeSignatureV1(c *gin.Context) error {
	ctx, span := dog.CreateSpan(c.Request.Context(), "GinKalkanServer_MakeSignatureV1")
	defer span.End()

	var request MakeSignatureRequestV1
	if err := c.ShouldBindJSON(&request); err != nil {
		return err
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.MD(c.Request.Header))

	response, err := s.client.MakeSignatureV1(ctx, &request)
	if err != nil {
		return err
	}

	c.JSON(http.StatusOK, &Kalkan_MakeSignatureV1_Success{
		Status:     true,
		Message:    "Success",
		StatusCode: 0,
		Result:     response,
	})
	return nil
}
