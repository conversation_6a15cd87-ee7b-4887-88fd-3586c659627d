edition = "2023";

package processing.transaction.transaction_status;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/descriptor.proto";
import "mvp/proto/refs.proto";

service TransactionStatus {
  rpc GetBatchTransactionWithStatuses(BatchTransactionRequestV1) returns(BatchTransactionResponseV1);
}

message TransactionStatusRef {
  string name = 2;
  string code = 3;
  bool is_final = 4;
}

extend google.protobuf.EnumValueOptions {
  TransactionStatusRef transaction_status_value = 200000;
}

extend google.protobuf.EnumOptions {
  TransactionStatusRef default_transaction_status_value = 200001;
}

enum EnumTransactionStatus {
  option(mvp.default_ref) = "default_transaction_status_value";
  option(mvp.ref) = "transaction_status_value";
  option(default_transaction_status_value) = {
    code: "error",
    name: "Произошла ошибка при проведении транзакции",
    is_final: false
  };
  Unknown = 0 [(transaction_status_value) = {
    code: "Unknown",
    name: "Unknown",
    is_final: false,
  }];
  TransactionStatusNew = 2 [(transaction_status_value) = {
    code: "new",
    name: "Транзакция создана",
    is_final: false,
  }];
  TransactionStatusThreeDSWaiting = 3 [(transaction_status_value) = {
    code: "threeds_waiting",
    name: "Транзакция ожидает ответа от 3ds сервера",
    is_final: false,
  }];
  TransactionStatusProcessed = 4 [(transaction_status_value) = {
    code: "processed",
    name: "Транзакция в работе",
    is_final: false,
  }];
  TransactionStatusFailed = 5 [(transaction_status_value) = {
    code: "failed",
    name: "Транзакция прошла неуспешно",
    is_final: true,
  }];
  TransactionStatusRefund = 6 [(transaction_status_value) = {
    code: "refund",
    name: "Транзакция возвращена",
    is_final: true,
  }];
  TransactionStatusCanceled = 7 [(transaction_status_value) = {
    code: "canceled",
    name: "Транзакция отменена",
    is_final: true,
  }];
  TransactionStatusRetry = 8 [(transaction_status_value) = {
    code: "retry",
    name: "Транзакция в статусе повтор",
    is_final: false,
  }];
  TransactionStatusSuccess = 9 [(transaction_status_value) = {
    code: "success",
    name: "Транзакция прошла успешно",
    is_final: true,
  }];
  TransactionStatusThreeDSReceived = 10 [(transaction_status_value) = {
    code: "threeds_received",
    name: "Транзакция получила ответ от 3ds сервера",
    is_final: false,
  }];
  TransactionStatusHolded = 11 [(transaction_status_value) = {
    code: "holded",
    name: "Транзакция в статусе ожидания",
    is_final: false,
  }];
  TransactionStatusRefundWaiting = 13 [(transaction_status_value) = {
    code: "refund_waiting",
    name: "Транзакция в статусе ожидания возврата",
    is_final: false,
  }];
  TransactionStatusAuthorized = 14 [(transaction_status_value) = {
    code: "authorized",
    name: "Транзакция авторизована",
    is_final: true,
  }];
  TransactionStatusError = 15 [(transaction_status_value) = {
    code: "error",
    name: "Произошла ошибка при проведении транзакции",
    is_final: false,
  }];
  TransactionStatusFingerPrint = 16 [(transaction_status_value) = {
    code: "fingerprint",
    name: "Ожидается проверка отпечатка браузера банком",
    is_final: false,
  }];
}

message BatchTransactionRequestV1 {
  repeated uint64 transaction_ids = 1;
}

message TransactionWithStatusResponseV1 {
  uint64 transaction_id = 1;
  string status_code = 2;
  bool is_final_status = 3;
}

message BatchTransactionResponseV1 {
  repeated TransactionWithStatusResponseV1 batch_response = 1;
}