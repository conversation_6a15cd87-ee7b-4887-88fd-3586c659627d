// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: inner/processing/grpc/epay_status.proto

package grpc

import (
	_ "git.local/sensitive/mvp/pb"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EnumEpayStatusCode int32

const (
	EnumEpayStatusCode_Charge     EnumEpayStatusCode = 0
	EnumEpayStatusCode_New        EnumEpayStatusCode = 1
	EnumEpayStatusCode_Refund     EnumEpayStatusCode = 2
	EnumEpayStatusCode_Cancel     EnumEpayStatusCode = 3
	EnumEpayStatusCode_Cancel_Old EnumEpayStatusCode = 4
	EnumEpayStatusCode_ThreeD     EnumEpayStatusCode = 5
	EnumEpayStatusCode_Reject     EnumEpayStatusCode = 6
	EnumEpayStatusCode_Auth       EnumEpayStatusCode = 7
	EnumEpayStatusCode_Failed     EnumEpayStatusCode = 8
)

// Enum value maps for EnumEpayStatusCode.
var (
	EnumEpayStatusCode_name = map[int32]string{
		0: "Charge",
		1: "New",
		2: "Refund",
		3: "Cancel",
		4: "Cancel_Old",
		5: "ThreeD",
		6: "Reject",
		7: "Auth",
		8: "Failed",
	}
	EnumEpayStatusCode_value = map[string]int32{
		"Charge":     0,
		"New":        1,
		"Refund":     2,
		"Cancel":     3,
		"Cancel_Old": 4,
		"ThreeD":     5,
		"Reject":     6,
		"Auth":       7,
		"Failed":     8,
	}
)

func (x EnumEpayStatusCode) Enum() *EnumEpayStatusCode {
	p := new(EnumEpayStatusCode)
	*p = x
	return p
}

func (x EnumEpayStatusCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnumEpayStatusCode) Descriptor() protoreflect.EnumDescriptor {
	return file_inner_processing_grpc_epay_status_proto_enumTypes[0].Descriptor()
}

func (EnumEpayStatusCode) Type() protoreflect.EnumType {
	return &file_inner_processing_grpc_epay_status_proto_enumTypes[0]
}

func (x EnumEpayStatusCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnumEpayStatusCode.Descriptor instead.
func (EnumEpayStatusCode) EnumDescriptor() ([]byte, []int) {
	return file_inner_processing_grpc_epay_status_proto_rawDescGZIP(), []int{0}
}

type EpayStatusCodeRef struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Status            *string                `protobuf:"bytes,1,opt,name=status" json:"status,omitempty"`
	TransactionStatus *EnumTransactionStatus `protobuf:"varint,2,opt,name=transaction_status,json=transactionStatus,enum=processing.transaction.transaction_status.EnumTransactionStatus" json:"transaction_status,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *EpayStatusCodeRef) Reset() {
	*x = EpayStatusCodeRef{}
	mi := &file_inner_processing_grpc_epay_status_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EpayStatusCodeRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EpayStatusCodeRef) ProtoMessage() {}

func (x *EpayStatusCodeRef) ProtoReflect() protoreflect.Message {
	mi := &file_inner_processing_grpc_epay_status_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EpayStatusCodeRef.ProtoReflect.Descriptor instead.
func (*EpayStatusCodeRef) Descriptor() ([]byte, []int) {
	return file_inner_processing_grpc_epay_status_proto_rawDescGZIP(), []int{0}
}

func (x *EpayStatusCodeRef) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *EpayStatusCodeRef) GetTransactionStatus() EnumTransactionStatus {
	if x != nil && x.TransactionStatus != nil {
		return *x.TransactionStatus
	}
	return EnumTransactionStatus_Unknown
}

var file_inner_processing_grpc_epay_status_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.EnumValueOptions)(nil),
		ExtensionType: (*EpayStatusCodeRef)(nil),
		Field:         200008,
		Name:          "processing.epay_status.epay.epay_status_code_value",
		Tag:           "bytes,200008,opt,name=epay_status_code_value",
		Filename:      "inner/processing/grpc/epay_status.proto",
	},
	{
		ExtendedType:  (*descriptorpb.EnumOptions)(nil),
		ExtensionType: (*EpayStatusCodeRef)(nil),
		Field:         200009,
		Name:          "processing.epay_status.epay.default_epay_status_code_value",
		Tag:           "bytes,200009,opt,name=default_epay_status_code_value",
		Filename:      "inner/processing/grpc/epay_status.proto",
	},
}

// Extension fields to descriptorpb.EnumValueOptions.
var (
	// optional processing.epay_status.epay.EpayStatusCodeRef epay_status_code_value = 200008;
	E_EpayStatusCodeValue = &file_inner_processing_grpc_epay_status_proto_extTypes[0]
)

// Extension fields to descriptorpb.EnumOptions.
var (
	// optional processing.epay_status.epay.EpayStatusCodeRef default_epay_status_code_value = 200009;
	E_DefaultEpayStatusCodeValue = &file_inner_processing_grpc_epay_status_proto_extTypes[1]
)

var File_inner_processing_grpc_epay_status_proto protoreflect.FileDescriptor

var file_inner_processing_grpc_epay_status_proto_rawDesc = string([]byte{
	0x0a, 0x27, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x65, 0x70, 0x61, 0x79, 0x1a, 0x2e, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d, 0x76, 0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x72, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x6d, 0x76,
	0x70, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9c, 0x01, 0x0a, 0x11, 0x45, 0x70, 0x61, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x66, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x6f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2a, 0xc4, 0x03, 0x0a, 0x12, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x70, 0x61, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x10, 0x00, 0x1a, 0x1a, 0xc2, 0xd4, 0x61, 0x0a, 0x0a, 0x06, 0x43, 0x48,
	0x41, 0x52, 0x47, 0x45, 0x10, 0x09, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x43, 0x48, 0x41, 0x52,
	0x47, 0x45, 0x12, 0x1d, 0x0a, 0x03, 0x4e, 0x65, 0x77, 0x10, 0x01, 0x1a, 0x14, 0xc2, 0xd4, 0x61,
	0x07, 0x0a, 0x03, 0x4e, 0x45, 0x57, 0x10, 0x0b, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x03, 0x4e, 0x45,
	0x57, 0x12, 0x26, 0x0a, 0x06, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x10, 0x02, 0x1a, 0x1a, 0xc2,
	0xd4, 0x61, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x06, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x06, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x12, 0x26, 0x0a, 0x06, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x10, 0x03, 0x1a, 0x1a, 0xc2, 0xd4, 0x61, 0x0a, 0x0a, 0x06, 0x43, 0x41, 0x4e,
	0x43, 0x45, 0x4c, 0x10, 0x07, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x43, 0x41, 0x4e, 0x43, 0x45,
	0x4c, 0x12, 0x32, 0x0a, 0x0a, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x4f, 0x6c, 0x64, 0x10,
	0x04, 0x1a, 0x22, 0xc2, 0xd4, 0x61, 0x0e, 0x0a, 0x0a, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x5f,
	0x4f, 0x4c, 0x44, 0x10, 0x0b, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x0a, 0x43, 0x41, 0x4e, 0x43, 0x45,
	0x4c, 0x5f, 0x4f, 0x4c, 0x44, 0x12, 0x1e, 0x0a, 0x06, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x10,
	0x05, 0x1a, 0x12, 0xc2, 0xd4, 0x61, 0x06, 0x0a, 0x02, 0x33, 0x44, 0x10, 0x03, 0xca, 0xa1, 0xec,
	0x8e, 0x02, 0x02, 0x33, 0x44, 0x12, 0x26, 0x0a, 0x06, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x10,
	0x06, 0x1a, 0x1a, 0xc2, 0xd4, 0x61, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x10,
	0x05, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x06, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x12, 0x20, 0x0a,
	0x04, 0x41, 0x75, 0x74, 0x68, 0x10, 0x07, 0x1a, 0x16, 0xc2, 0xd4, 0x61, 0x08, 0x0a, 0x04, 0x41,
	0x55, 0x54, 0x48, 0x10, 0x0e, 0xca, 0xa1, 0xec, 0x8e, 0x02, 0x04, 0x41, 0x55, 0x54, 0x48, 0x12,
	0x26, 0x0a, 0x06, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x08, 0x1a, 0x1a, 0xc2, 0xd4, 0x61,
	0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x0e, 0xca, 0xa1, 0xec, 0x8e, 0x02,
	0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x1a, 0x51, 0xca, 0xd4, 0x61, 0x0d, 0x0a, 0x09, 0x75,
	0x6e, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x10, 0x0b, 0xaa, 0x82, 0xec, 0x8e, 0x02, 0x1e,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0xb2, 0x82,
	0xec, 0x8e, 0x02, 0x16, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x88, 0x01, 0x0a, 0x16, 0x65,
	0x70, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xc8, 0x9a, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x70,
	0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x65, 0x70, 0x61, 0x79, 0x2e, 0x45,
	0x70, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x66,
	0x52, 0x13, 0x65, 0x70, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x92, 0x01, 0x0a, 0x1e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xc9, 0x9a, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x70, 0x61, 0x79,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x65, 0x70, 0x61, 0x79, 0x2e, 0x45, 0x70, 0x61,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x66, 0x52, 0x1a,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x45, 0x70, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x2d, 0x5a, 0x2b, 0x67, 0x69,
	0x74, 0x2e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76,
	0x65, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
})

var (
	file_inner_processing_grpc_epay_status_proto_rawDescOnce sync.Once
	file_inner_processing_grpc_epay_status_proto_rawDescData []byte
)

func file_inner_processing_grpc_epay_status_proto_rawDescGZIP() []byte {
	file_inner_processing_grpc_epay_status_proto_rawDescOnce.Do(func() {
		file_inner_processing_grpc_epay_status_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_epay_status_proto_rawDesc), len(file_inner_processing_grpc_epay_status_proto_rawDesc)))
	})
	return file_inner_processing_grpc_epay_status_proto_rawDescData
}

var file_inner_processing_grpc_epay_status_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_inner_processing_grpc_epay_status_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_inner_processing_grpc_epay_status_proto_goTypes = []any{
	(EnumEpayStatusCode)(0),               // 0: processing.epay_status.epay.EnumEpayStatusCode
	(*EpayStatusCodeRef)(nil),             // 1: processing.epay_status.epay.EpayStatusCodeRef
	(EnumTransactionStatus)(0),            // 2: processing.transaction.transaction_status.EnumTransactionStatus
	(*descriptorpb.EnumValueOptions)(nil), // 3: google.protobuf.EnumValueOptions
	(*descriptorpb.EnumOptions)(nil),      // 4: google.protobuf.EnumOptions
}
var file_inner_processing_grpc_epay_status_proto_depIdxs = []int32{
	2, // 0: processing.epay_status.epay.EpayStatusCodeRef.transaction_status:type_name -> processing.transaction.transaction_status.EnumTransactionStatus
	3, // 1: processing.epay_status.epay.epay_status_code_value:extendee -> google.protobuf.EnumValueOptions
	4, // 2: processing.epay_status.epay.default_epay_status_code_value:extendee -> google.protobuf.EnumOptions
	1, // 3: processing.epay_status.epay.epay_status_code_value:type_name -> processing.epay_status.epay.EpayStatusCodeRef
	1, // 4: processing.epay_status.epay.default_epay_status_code_value:type_name -> processing.epay_status.epay.EpayStatusCodeRef
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	3, // [3:5] is the sub-list for extension type_name
	1, // [1:3] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_inner_processing_grpc_epay_status_proto_init() }
func file_inner_processing_grpc_epay_status_proto_init() {
	if File_inner_processing_grpc_epay_status_proto != nil {
		return
	}
	file_inner_processing_grpc_transaction_status_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inner_processing_grpc_epay_status_proto_rawDesc), len(file_inner_processing_grpc_epay_status_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 2,
			NumServices:   0,
		},
		GoTypes:           file_inner_processing_grpc_epay_status_proto_goTypes,
		DependencyIndexes: file_inner_processing_grpc_epay_status_proto_depIdxs,
		EnumInfos:         file_inner_processing_grpc_epay_status_proto_enumTypes,
		MessageInfos:      file_inner_processing_grpc_epay_status_proto_msgTypes,
		ExtensionInfos:    file_inner_processing_grpc_epay_status_proto_extTypes,
	}.Build()
	File_inner_processing_grpc_epay_status_proto = out.File
	file_inner_processing_grpc_epay_status_proto_goTypes = nil
	file_inner_processing_grpc_epay_status_proto_depIdxs = nil
}
