// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package cobra

import (
	bytes "bytes"
	context "context"
	json "encoding/json"
	errors "errors"
	fmt "fmt"
	grpc "git.local/sensitive/innerpb/processing/grpc"
	cntx "git.local/sensitive/mvp/pkg/cntx"
	errors1 "git.local/sensitive/mvp/pkg/errors"
	gtransaction "git.local/sensitive/pkg/gtransaction"
	http_logger "git.local/sensitive/pkg/http_logger"
	consumer "git.local/sensitive/processing/transaction/delivery/event/consumer"
	v1 "git.local/sensitive/processing/transaction/delivery/grpc/v1"
	http "git.local/sensitive/processing/transaction/delivery/http"
	database "git.local/sensitive/processing/transaction/repository/database"
	rest "git.local/sensitive/processing/transaction/repository/rest"
	service "git.local/sensitive/processing/transaction/service"
	usecase "git.local/sensitive/processing/transaction/usecase"
	watermill "github.com/ThreeDotsLabs/watermill"
	message "github.com/ThreeDotsLabs/watermill/message"
	middleware "github.com/ThreeDotsLabs/watermill/message/router/middleware"
	v3 "github.com/cenkalti/backoff/v3"
	sentry_go "github.com/getsentry/sentry-go"
	otel "github.com/getsentry/sentry-go/otel"
	redis "github.com/gin-contrib/sessions/redis"
	gin "github.com/gin-gonic/gin"
	go_grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	ctxzap "github.com/grpc-ecosystem/go-grpc-middleware/logging/zap/ctxzap"
	prometheus1 "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	tags "github.com/grpc-ecosystem/go-grpc-middleware/tags"
	api "github.com/hashicorp/vault/api"
	kubernetes "github.com/hashicorp/vault/api/auth/kubernetes"
	grpc_middleware_sentry "github.com/johnbellone/grpc-middleware-sentry"
	nats_go "github.com/nats-io/nats.go"
	prometheus "github.com/prometheus/client_golang/prometheus"
	collectors "github.com/prometheus/client_golang/prometheus/collectors"
	promhttp "github.com/prometheus/client_golang/prometheus/promhttp"
	v91 "github.com/redis/go-redis/extra/redisotel/v9"
	v9 "github.com/redis/go-redis/v9"
	cobra "github.com/spf13/cobra"
	mongo "go.mongodb.org/mongo-driver/mongo"
	options "go.mongodb.org/mongo-driver/mongo/options"
	otelmongo "go.opentelemetry.io/contrib/instrumentation/go.mongodb.org/mongo-driver/mongo/otelmongo"
	otelgrpc "go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	otel1 "go.opentelemetry.io/otel"
	attribute "go.opentelemetry.io/otel/attribute"
	jaeger "go.opentelemetry.io/otel/exporters/jaeger"
	propagation "go.opentelemetry.io/otel/propagation"
	resource "go.opentelemetry.io/otel/sdk/resource"
	trace "go.opentelemetry.io/otel/sdk/trace"
	v1_7_0 "go.opentelemetry.io/otel/semconv/v1.7.0"
	trace1 "go.opentelemetry.io/otel/trace"
	zap "go.uber.org/zap"
	zapcore "go.uber.org/zap/zapcore"
	grpc1 "google.golang.org/grpc"
	reflection "google.golang.org/grpc/reflection"
	postgres "gorm.io/driver/postgres"
	gorm "gorm.io/gorm"
	dbresolver "gorm.io/plugin/dbresolver"
	tracing "gorm.io/plugin/opentelemetry/tracing"
	io "io"
	net "net"
	http1 "net/http"
	httputil "net/http/httputil"
	os "os"
	signal "os/signal"
	debug "runtime/debug"
	"strconv"
	strings "strings"
	sync "sync"
	syscall "syscall"
	time "time"
)

import (
	sentrygin "github.com/getsentry/sentry-go/gin"
	otelgin "go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

var isInitTransactionService bool

type sensitiveConfigTransactionService struct {
	MONGO_DSN          string   `json:"MONGO_DSN"`
	DATABASE_READ_DSN  []string `json:"DATABASE_READ_DSN"`
	DATABASE_WRITE_DSN []string `json:"DATABASE_WRITE_DSN"`
	REDIS_PASSWORD     string   `json:"REDIS_PASSWORD"`
	REDIS_DSN          string   `json:"REDIS_DSN"`
	LOG_LEVEL          string   `json:"LOG_LEVEL"`
	JAEGER_DSN         string   `json:"JAEGER_DSN"`
	SENTRY_DSN         string   `json:"SENTRY_DSN"`
	NATS_DSN           string   `json:"NATS_DSN"`
}

func (c sensitiveConfigTransactionService) Check() {
	if c.MONGO_DSN == "" {
		panic("empty MONGO_DSN")
	}
	if len(c.DATABASE_READ_DSN) == 0 {
		panic("empty DATABASE_READ_DSN")
	}
	if len(c.DATABASE_WRITE_DSN) == 0 {
		panic("empty DATABASE_WRITE_DSN")
	}
	if c.REDIS_PASSWORD == "" {
		panic("empty REDIS_PASSWORD")
	}
	if c.REDIS_DSN == "" {
		panic("empty REDIS_DSN")
	}

	if c.JAEGER_DSN == "" {
		panic("empty JAEGER_DSN")
	}
	if c.SENTRY_DSN == "" {
		panic("empty SENTRY_DSN")
	}
	if c.NATS_DSN == "" {
		panic("empty NATS_DSN")
	}

}

type configTransactionService struct {
	BACKOFF_INITIAL_INTERVAL     string `json:"BACKOFF_INITIAL_INTERVAL"`
	BACKOFF_MULTIPLIER           string `json:"BACKOFF_MULTIPLIER"`
	BACKOFF_MAX_INTERVAL         string `json:"BACKOFF_MAX_INTERVAL"`
	BACKOFF_MAX_ELAPSED_TIME     string `json:"BACKOFF_MAX_ELAPSED_TIME"`
	BACKOFF_RANDOMIZATION_FACTOR string `json:"BACKOFF_RANDOMIZATION_FACTOR"`
	MONGO_COLLECTION             string `json:"MONGO_COLLECTION"`
	REDIS_DSN                    string `json:"REDIS_DSN"`
	REDIS_PASSWORD               string `json:"REDIS_PASSWORD"`
	SESSION_KEY                  string `json:"SESSION_KEY"`
	SESSION_SECRET               string `json:"SESSION_SECRET"`
	HTTP_PORT                    string `json:"HTTP_PORT"`
	GRPC_PORT                    string `json:"GRPC_PORT"`
	LIBRE_OFFICE_URL             string `json:"LIBRE_OFFICE_URL"`
	PAYMENT_VIEW_URL             string `json:"PAYMENT_VIEW_URL"`
	THREEDS_PATH_V1              string `json:"THREEDS_PATH_V1"`
	RECEIPT_HTML_FILE            string `json:"RECEIPT_HTML_FILE"`
	EMAIL_SENDER                 string `json:"EMAIL_SENDER"`
	RECEIPT_TITLE                string `json:"RECEIPT_TITLE"`
	CDN_URL                      string `json:"CDN_URL"`
	DOMAIN                       string `json:"DOMAIN"`
	GOOGLE_PAY_GATEWAY           string `json:"GOOGLE_PAY_GATEWAY"`
}

func (c *configTransactionService) Check() {

	if c.MONGO_COLLECTION == "" {
		panic("empty MONGO_COLLECTION ")
	}
	if c.BACKOFF_INITIAL_INTERVAL == "0" {
		panic("empty BACKOFF_INITIAL_INTERVAL")
	}
	if c.BACKOFF_MULTIPLIER == "0" {
		panic("empty BACKOFF_MULTIPLIER")
	}
	if c.BACKOFF_MAX_INTERVAL == "0" {
		panic("empty BACKOFF_MAX_INTERVAL")
	}
	if c.BACKOFF_MAX_ELAPSED_TIME == "0" {
		panic("empty BACKOFF_MAX_ELAPSED_TIME")
	}
	if c.BACKOFF_RANDOMIZATION_FACTOR == "0" {
		panic("empty BACKOFF_RANDOMIZATION_FACTOR")
	}
	if c.MONGO_COLLECTION == "" {
		panic("empty MONGO_COLLECTION")
	}
	if c.REDIS_DSN == "" {
		panic("empty REDIS_DSN")
	}
	if c.REDIS_PASSWORD == "" {
		panic("empty REDIS_PASSWORD")
	}
	if c.SESSION_KEY == "" {
		panic("empty SESSION_KEY")
	}
	if c.SESSION_SECRET == "" {
		panic("empty SESSION_SECRET")
	}
	if c.HTTP_PORT == "" {
		panic("empty HTTP_PORT")
	}
	if c.GRPC_PORT == "" {
		panic("empty GRPC_PORT")
	}
	if c.RECEIPT_TITLE == "" {
		panic("empty RECEIPT_TITLE")
	}
	if c.CDN_URL == "" {
		panic("empty CDN_URL")
	}
	if c.LIBRE_OFFICE_URL == "" {
		panic("empty LIBRE_OFFICE_URL")
	}
	if c.PAYMENT_VIEW_URL == "" {
		panic("empty PAYMENT_VIEW_URL")
	}
	if c.THREEDS_PATH_V1 == "" {
		panic("empty THREEDS_PATH_V1")
	}
	if c.RECEIPT_HTML_FILE == "" {
		panic("empty RECEIPT_HTML_FILE")
	}
	if c.EMAIL_SENDER == "" {
		panic("empty EMAIL_SENDER")
	}
	if c.DOMAIN == "" {
		panic("empty DOMAIN")
	}
	if c.GOOGLE_PAY_GATEWAY == "" {
		panic("empty GOOGLE_PAY_GATEWAY")
	}
}

func RunTransactionService(cmd *cobra.Command, args []string) {
	defer func() {
		if err, hub := recover(), sentry_go.CurrentHub(); err != nil && hub != nil {
			hub.Recover(err)
			sentry_go.Flush(5 * time.Second)
			panic(err)
		}
	}()

	ctx, cancel, ginEngine, gormDB, mongoClient, natsMessageRouter, redisClient, jetStreamContext, natsConn, stand, healthCheckStore, config, microservice, grpcServer := InitTransactionService("processing.transaction.transaction.Transaction")

	logger := zap.L()

	logger.Info("begin register db healthCheck")
	registerDBHealthCheckTransactionService(gormDB, healthCheckStore)
	logger.Info("end register db healthCheck")

	hl := http_logger.NewHttpLogMongo(mongoClient.Database(microservice), config.MONGO_COLLECTION)

	b := v3.NewExponentialBackOff()
	bii, _ := strconv.Atoi(config.BACKOFF_INITIAL_INTERVAL)
	bmei, _ := strconv.Atoi(config.BACKOFF_MAX_ELAPSED_TIME)
	bmi, _ := strconv.Atoi(config.BACKOFF_MAX_INTERVAL)
	bm, _ := strconv.ParseFloat(config.BACKOFF_MULTIPLIER, 64)
	brf, _ := strconv.ParseFloat(config.BACKOFF_RANDOMIZATION_FACTOR, 64)

	b.InitialInterval = time.Duration(bii) * time.Second
	b.Multiplier = bm
	b.MaxInterval = time.Duration(bmi) * time.Second
	b.MaxElapsedTime = time.Duration(bmei) * time.Second
	b.RandomizationFactor = brf

	gtxManager := gtransaction.NewGormTransactionManager(gormDB)
	repoTransactionBaseDB := database.NewTransactionBaseDB(
		gormDB,
	)

	repoTransactionTypeDB := database.NewTransactionTypeDB(
		gormDB,
	)

	repoAggregatedTransactionTypeDB := database.NewAggregatedTransactionTypeDB(
		gormDB,
	)

	repoAggregatedTypeDB := database.NewAggregatedTypeDB(
		gormDB,
	)

	repoViewInfoDB := database.NewViewInfoDB(
		gormDB,
	)

	projecttransactionsClient, err := grpc.NewPreparedProjectTransactionsClient()
	if err != nil {
		logger.Panic("error connecting to ProjectTransactions service", zap.Error(err))
	}
	grpc.NewLoggedProjectTransactionsClient(projecttransactionsClient)

	cardClient, err := grpc.NewPreparedCardClient()
	if err != nil {
		logger.Panic("error connecting to Card service", zap.Error(err))
	}
	grpc.NewLoggedCardClient(cardClient)

	commissionClient, err := grpc.NewPreparedCommissionClient()
	if err != nil {
		logger.Panic("error connecting to Commission service", zap.Error(err))
	}
	grpc.NewLoggedCommissionClient(commissionClient)

	merchantClient, err := grpc.NewPreparedMerchantClient()
	if err != nil {
		logger.Panic("error connecting to Merchant service", zap.Error(err))
	}
	grpc.NewLoggedMerchantClient(merchantClient)

	terminalClient, err := grpc.NewPreparedTerminalClient()
	if err != nil {
		logger.Panic("error connecting to Terminal service", zap.Error(err))
	}
	grpc.NewLoggedTerminalClient(terminalClient)

	serviceTransactionService := service.NewTransactionService(
		repoTransactionBaseDB,
		repoTransactionTypeDB,
		repoAggregatedTransactionTypeDB,
		repoAggregatedTypeDB,
		repoViewInfoDB,
		projecttransactionsClient,
		cardClient,
		commissionClient,
		merchantClient,
		terminalClient,
	)

	repoTransactionInfoDB := database.NewTransactionInfoDB(
		gormDB,
	)

	repoAdditionalDataDB := database.NewAdditionalDataDB(
		gormDB,
	)

	repoTransactionCallbackStatusDB := database.NewTransactionCallbackStatusDB(
		gormDB,
	)

	repoProjectRest := rest.NewProjectRest(
		b,
		hl,
	)

	serviceTransactionCallBackService := service.NewTransactionCallBackService(
		repoTransactionBaseDB,
		repoTransactionInfoDB,
		repoAdditionalDataDB,
		repoTransactionCallbackStatusDB,
		repoProjectRest,
		merchantClient,
		cardClient,
		terminalClient,
	)

	serviceTransactionVerifier := service.NewTransactionVerifier(
		repoProjectRest,
		cardClient,
		projecttransactionsClient,
		merchantClient,
	)

	repoTransactionDB := database.NewTransactionDB(
		gormDB,
	)

	serviceAcquirerService := service.NewAcquirerService(
		repoTransactionInfoDB,
		repoTransactionDB,
		terminalClient,
	)

	repoOperationDB := database.NewOperationDB(
		gormDB,
	)

	serviceTwoStageVerifierService := service.NewTwoStageVerifierService(
		repoOperationDB,
		repoProjectRest,
		merchantClient,
	)

	serviceOperationService := service.NewOperationService(
		repoOperationDB,
	)

	repoOperationTypeDB := database.NewOperationTypeDB(
		gormDB,
	)

	multiacquiringClient, err := grpc.NewPreparedMultiAcquiringClient()
	if err != nil {
		logger.Panic("error connecting to MultiAcquiring service", zap.Error(err))
	}
	grpc.NewLoggedMultiAcquiringClient(multiacquiringClient)

	serviceTwoStageManagerService := service.NewTwoStageManagerService(
		repoTransactionDB,
		repoOperationTypeDB,
		repoOperationDB,
		multiacquiringClient,
	)

	billingClient, err := grpc.NewPreparedBillingClient()
	if err != nil {
		logger.Panic("error connecting to Billing service", zap.Error(err))
	}
	grpc.NewLoggedBillingClient(billingClient)

	serviceBillingService := service.NewBillingService(
		repoTransactionBaseDB,
		billingClient,
		commissionClient,
	)

	useCaseTwoStageService := usecase.NewTwoStageServiceUseCase(
		serviceTransactionService,
		serviceTransactionCallBackService,
		serviceTransactionVerifier,
		serviceAcquirerService,
		serviceTwoStageVerifierService,
		serviceOperationService,
		serviceTwoStageManagerService,
		serviceBillingService,
	)

	repoTransactionAnchorerDB := database.NewTransactionAnchorerDB(
		gormDB,
	)

	refundClient, err := grpc.NewPreparedRefundClient()
	if err != nil {
		logger.Panic("error connecting to Refund service", zap.Error(err))
	}
	grpc.NewLoggedRefundClient(refundClient)

	serviceTransactionAnchorerService := service.NewTransactionAnchorerService(
		repoTransactionAnchorerDB,
		cardClient,
		terminalClient,
		refundClient,
	)

	useCaseTransactionAnchorService := usecase.NewTransactionAnchorServiceUseCase(
		serviceTransactionAnchorerService,
	)

	serviceAggregatedTransactionTypeService := service.NewAggregatedTransactionTypeService(
		repoAggregatedTransactionTypeDB,
		repoAggregatedTypeDB,
	)

	useCaseAggregatedTransactionTypeService := usecase.NewAggregatedTransactionTypeServiceUseCase(
		serviceAggregatedTransactionTypeService,
	)

	serviceProjectService := service.NewProjectService(
		repoProjectRest,
		merchantClient,
	)

	useCaseAuthorize := usecase.NewAuthorizeUseCase(
		serviceTransactionService,
		serviceTransactionVerifier,
		serviceProjectService,
		serviceTransactionAnchorerService,
	)

	serviceTransactionStatusUpdaterService := service.NewTransactionStatusUpdaterService(
		repoTransactionBaseDB,
		repoTransactionDB,
		repoAggregatedTransactionTypeDB,
		repoAggregatedTypeDB,
		gtxManager,
		billingClient,
	)

	repoTransactionsStatuserDB := database.NewTransactionsStatuserDB(
		gormDB,
	)

	serviceTransactionsStatuserService := service.NewTransactionsStatuserService(
		repoTransactionsStatuserDB,
		repoTransactionBaseDB,
	)

	servicePayOutManagerService := service.NewPayOutManagerService(
		repoTransactionBaseDB,
		repoTransactionInfoDB,
		repoTransactionDB,
		repoAggregatedTransactionTypeDB,
		repoTransactionTypeDB,
		gtxManager,
		multiacquiringClient,
	)

	repoLibreOfficeRest := rest.NewLibreOfficeRest(
		hl,
		config.LIBRE_OFFICE_URL,
	)

	viewcrafterClient, err := grpc.NewPreparedViewCrafterClient()
	if err != nil {
		logger.Panic("error connecting to ViewCrafter service", zap.Error(err))
	}
	grpc.NewLoggedViewCrafterClient(viewcrafterClient)

	serviceReceiptService := service.NewReceiptService(
		repoLibreOfficeRest,
		jetStreamContext,
		config.RECEIPT_HTML_FILE,
		config.EMAIL_SENDER,
		config.RECEIPT_TITLE,
		config.CDN_URL,
		viewcrafterClient,
	)

	serviceAdditionalDataService := service.NewAdditionalDataService(
		repoAdditionalDataDB,
	)

	serviceCommissionService := service.NewCommissionService(
		repoAggregatedTransactionTypeDB,
		repoTransactionBaseDB,
		repoTransactionDB,
		commissionClient,
	)

	serviceTransactionHelperService := service.NewTransactionHelperService(
		config.PAYMENT_VIEW_URL,
	)

	serviceTransactionTypeService := service.NewTransactionTypeService(
		repoTransactionTypeDB,
	)

	repoTransactionCardInfoDB := database.NewTransactionCardInfoDB(
		gormDB,
	)

	serviceCardSetterService := service.NewCardSetterService(
		repoTransactionCardInfoDB,
		cardClient,
	)

	serviceCardGetterService := service.NewCardGetterService(
		cardClient,
		terminalClient,
	)

	serviceCardProcessorService := service.NewCardProcessorService(
		jetStreamContext,
		cardClient,
	)

	serviceThreeDSConfirmService := service.NewThreeDSConfirmService(
		repoTransactionDB,
		multiacquiringClient,
	)

	servicePayInManagerService := service.NewPayInManagerService(
		repoTransactionInfoDB,
		repoAggregatedTransactionTypeDB,
		repoTransactionDB,
		repoTransactionBaseDB,
		gtxManager,
		jetStreamContext,
		config.DOMAIN,
		config.THREEDS_PATH_V1,
		multiacquiringClient,
	)

	repoTransactionJobsDB := database.NewTransactionJobsDB(
		gormDB,
	)

	serviceTransactionInfoService := service.NewTransactionInfoService(
		repoTransactionInfoDB,
		repoTransactionJobsDB,
	)

	serviceTransactionUpdateService := service.NewTransactionUpdateService(
		repoTransactionDB,
	)

	projectClient, err := grpc.NewPreparedProjectClient()
	if err != nil {
		logger.Panic("error connecting to Project service", zap.Error(err))
	}
	grpc.NewLoggedProjectClient(projectClient)

	serviceProjectSettingService := service.NewProjectSettingService(
		projectClient,
	)

	collectorClient, err := grpc.NewPreparedCollectorClient()
	if err != nil {
		logger.Panic("error connecting to Collector service", zap.Error(err))
	}
	grpc.NewLoggedCollectorClient(collectorClient)

	serviceAntiFraudManagerService := service.NewAntiFraudManagerService(
		repoAggregatedTransactionTypeDB,
		repoAggregatedTypeDB,
		collectorClient,
	)

	useCasePayInService := usecase.NewPayInServiceUseCase(
		serviceProjectService,
		serviceTransactionHelperService,
		serviceTransactionVerifier,
		serviceTransactionTypeService,
		serviceTransactionService,
		serviceCardSetterService,
		serviceCardGetterService,
		serviceCardProcessorService,
		serviceAcquirerService,
		serviceThreeDSConfirmService,
		serviceTransactionCallBackService,
		servicePayInManagerService,
		serviceAdditionalDataService,
		serviceTransactionInfoService,
		serviceCommissionService,
		serviceReceiptService,
		serviceTransactionUpdateService,
		serviceProjectSettingService,
		serviceAntiFraudManagerService,
		serviceBillingService,
	)

	useCasePayOutService := usecase.NewPayOutServiceUseCase(
		serviceProjectService,
		serviceTransactionHelperService,
		serviceTransactionVerifier,
		serviceTransactionService,
		serviceCardSetterService,
		serviceCardGetterService,
		serviceCardProcessorService,
		serviceAcquirerService,
		serviceTransactionCallBackService,
		servicePayOutManagerService,
		serviceAdditionalDataService,
		serviceTransactionInfoService,
		serviceTransactionTypeService,
		serviceReceiptService,
		serviceCommissionService,
		serviceProjectSettingService,
		serviceAntiFraudManagerService,
		serviceBillingService,
	)

	useCaseThreeDS := usecase.NewThreeDSUseCase(
		serviceProjectService,
		serviceTransactionHelperService,
		serviceTransactionVerifier,
		serviceAcquirerService,
		serviceTransactionService,
		serviceThreeDSConfirmService,
		serviceTransactionCallBackService,
		servicePayInManagerService,
		serviceCardSetterService,
		serviceCardGetterService,
		serviceCardProcessorService,
		serviceReceiptService,
		serviceProjectSettingService,
		serviceBillingService,
		jetStreamContext,
	)

	smartpayClient, err := grpc.NewPreparedSmartPayClient()
	if err != nil {
		logger.Panic("error connecting to SmartPay service", zap.Error(err))
	}
	grpc.NewLoggedSmartPayClient(smartpayClient)

	serviceApplePayManagerService := service.NewApplePayManagerService(
		repoTransactionDB,
		repoTransactionTypeDB,
		repoAggregatedTransactionTypeDB,
		smartpayClient,
		multiacquiringClient,
		cardClient,
	)

	useCaseApplePayService := usecase.NewApplePayServiceUseCase(
		serviceProjectService,
		serviceTransactionHelperService,
		serviceTransactionVerifier,
		serviceTransactionService,
		serviceCardSetterService,
		serviceCardGetterService,
		serviceCardProcessorService,
		serviceAcquirerService,
		serviceThreeDSConfirmService,
		serviceTransactionCallBackService,
		servicePayInManagerService,
		serviceApplePayManagerService,
		serviceTransactionTypeService,
		serviceCommissionService,
		serviceAdditionalDataService,
		serviceAntiFraudManagerService,
		serviceBillingService,
	)

	serviceGooglePayManagerService := service.NewGooglePayManagerService(
		repoTransactionTypeDB,
		repoTransactionDB,
		repoAggregatedTransactionTypeDB,
		config.DOMAIN,
		config.THREEDS_PATH_V1,
		config.GOOGLE_PAY_GATEWAY,
		smartpayClient,
		merchantClient,
		multiacquiringClient,
	)

	useCaseGooglePayService := usecase.NewGooglePayServiceUseCase(
		serviceProjectService,
		servicePayInManagerService,
		serviceTransactionHelperService,
		serviceTransactionVerifier,
		serviceTransactionService,
		serviceCardSetterService,
		serviceCardGetterService,
		serviceCardProcessorService,
		serviceAcquirerService,
		serviceThreeDSConfirmService,
		serviceTransactionCallBackService,
		serviceGooglePayManagerService,
		serviceTransactionTypeService,
		serviceCommissionService,
		serviceAdditionalDataService,
		serviceAntiFraudManagerService,
		serviceBillingService,
	)

	serviceCardLinkManagerService := service.NewCardLinkManagerService(
		repoTransactionBaseDB,
		repoTransactionInfoDB,
		repoAdditionalDataDB,
		gtxManager,
	)

	serviceRefundManagerService := service.NewRefundManagerService(
		jetStreamContext,
		refundClient,
	)

	useCaseCardLinkService := usecase.NewCardLinkServiceUseCase(
		serviceProjectService,
		serviceTransactionHelperService,
		serviceTransactionVerifier,
		serviceTransactionService,
		serviceTransactionInfoService,
		serviceCardSetterService,
		serviceCardGetterService,
		serviceCardProcessorService,
		serviceAcquirerService,
		serviceThreeDSConfirmService,
		serviceTransactionCallBackService,
		serviceCardLinkManagerService,
		servicePayInManagerService,
		serviceRefundManagerService,
		serviceCommissionService,
		serviceAntiFraudManagerService,
		serviceBillingService,
	)

	repoTransactionStatusDB := database.NewTransactionStatusDB(
		gormDB,
	)

	serviceTransactionStatusService := service.NewTransactionStatusService(
		repoTransactionStatusDB,
		repoTransactionBaseDB,
		repoTransactionInfoDB,
		repoAggregatedTransactionTypeDB,
		repoAggregatedTypeDB,
		multiacquiringClient,
		terminalClient,
	)

	useCaseStatus := usecase.NewStatusUseCase(
		serviceTransactionService,
		serviceTransactionStatusService,
		serviceTransactionStatusUpdaterService,
	)

	useCaseType := usecase.NewTypeUseCase(
		serviceTransactionTypeService,
	)

	useCaseTransactionService := usecase.NewTransactionServiceUseCase(
		serviceTransactionService,
		serviceTransactionTypeService,
		serviceTransactionCallBackService,
		serviceTransactionAnchorerService,
		serviceProjectService,
		serviceTransactionHelperService,
		serviceAcquirerService,
		serviceRefundManagerService,
		serviceReceiptService,
	)

	useCaseClientCardService := usecase.NewClientCardServiceUseCase(
		serviceProjectService,
		serviceCardSetterService,
		serviceCardGetterService,
		serviceCardProcessorService,
	)

	useCaseAdditionalDataService := usecase.NewAdditionalDataServiceUseCase(
		serviceTransactionService,
		serviceAdditionalDataService,
	)

	repoProjectOrderDB := database.NewProjectOrderDB(
		gormDB,
	)

	serviceProjectOrderService := service.NewProjectOrderService(
		repoProjectOrderDB,
	)

	useCaseProjectOrderService := usecase.NewProjectOrderServiceUseCase(
		serviceProjectOrderService,
	)

	useCaseDonationService := usecase.NewDonationServiceUseCase(
		serviceProjectService,
		serviceTransactionHelperService,
		serviceTransactionVerifier,
		serviceTransactionTypeService,
		serviceTransactionService,
		serviceAcquirerService,
		serviceThreeDSConfirmService,
		serviceTransactionCallBackService,
		servicePayInManagerService,
		serviceAdditionalDataService,
		serviceTransactionInfoService,
		serviceCommissionService,
		serviceReceiptService,
	)

	useCaseReceiptService := usecase.NewReceiptServiceUseCase(
		serviceTransactionService,
		serviceReceiptService,
	)

	_, cErr := consumer.NewHandler(
		serviceTransactionStatusUpdaterService,
		jetStreamContext,
		natsConn,
		natsMessageRouter,
		ctx,
	)

	if cErr != nil {
		logger.Panic("cannot create event consumer", zap.Error(cErr))
		return
	}

	go func() {
		defer cancel()

		logger.Info("start nats message engine")
		if err := natsMessageRouter.Run(ctx); err != nil {
			logger.Panic("can not run nats message engine", zap.Error(err))
		}
	}()

	go func() {
		defer cancel()

		serverAddr := config.HTTP_PORT
		logger.Info("serverAdr", zap.String("HTTP_PORT", serverAddr))

		handlerDelivery := http.NewHandlerDelivery(
			useCasePayInService,
			useCasePayOutService,
			useCaseThreeDS,
			useCaseApplePayService,
			useCaseGooglePayService,
			useCaseCardLinkService,
			useCaseStatus,
			useCaseType,
			useCaseTransactionService,
			useCaseTwoStageService,
			useCaseClientCardService,
			useCaseAuthorize,
			useCaseAdditionalDataService,
			useCaseProjectOrderService,
			useCaseTransactionAnchorService,
			useCaseDonationService,
			useCaseAggregatedTransactionTypeService,
			useCaseReceiptService,
			microservice,
			redisClient,
			config.PAYMENT_VIEW_URL,
			jetStreamContext,
		)

		store, sErr := redis.NewStoreWithDB(
			10,
			"tcp",
			config.REDIS_DSN,
			config.REDIS_PASSWORD,
			"1",
			[]byte(config.SESSION_KEY),
		)
		if sErr != nil {
			logger.Panic("cannot create redis connection", zap.Error(sErr))
			return
		}

		handlerDelivery.Init(
			ginEngine,
			stand,
			config.SESSION_SECRET,
			store,
		)

		go func() {
			<-time.NewTicker(time.Second).C
			gin.SetMode(gin.ReleaseMode)
		}()

		if err := ginEngine.Run(":" + serverAddr); err != nil {
			logger.Panic("can not run gin engine", zap.Error(err))
		}
	}()

	go func() {
		defer cancel()

		grpc.RegisterTransactionServer(grpcServer,
			grpc.NewLoggedTransactionServer(
				v1.NewTransactionServer(
					useCaseTwoStageService,
					useCaseTransactionAnchorService,
					useCaseAggregatedTransactionTypeService,
					useCaseAuthorize,
					serviceTransactionStatusUpdaterService,
					serviceTransactionCallBackService,
					serviceTransactionsStatuserService,
					serviceTransactionService,
					servicePayOutManagerService,
					serviceAcquirerService,
					serviceReceiptService,
					serviceAdditionalDataService,
					serviceCommissionService,
					serviceBillingService,
					projectClient,
				),
			),
		)

		serverAddr := config.GRPC_PORT
		logger.Info("serverAdr", zap.String("GRPC_PORT", serverAddr))

		listenerGrpc, err := net.Listen("tcp", ":"+serverAddr)
		if err != nil {
			logger.Fatal("can not prepare net.Listener for grpc service", zap.Error(err))
		}

		reflection.Register(grpcServer)

		if err := grpcServer.Serve(listenerGrpc); err != nil {
			logger.Fatal("can not run grpc server", zap.Error(err))
		}
	}()

	<-ctx.Done()
}

func InitTransactionService(appName string) (
	context.Context,
	context.CancelFunc,
	*gin.Engine,
	*gorm.DB,
	*mongo.Client,
	*message.Router,
	*v9.Client,
	nats_go.JetStreamContext,
	*nats_go.Conn,
	string,
	*healthCheckStoreTransactionService,
	configTransactionService,
	string,
	*grpc1.Server,
) {
	if isInitTransactionService {
		panic("already init")
	}
	isInitTransactionService = true

	stand := os.Getenv("ENVIRONMENT")

	// Stand
	switch stand {
	case "local", "dev", "stage", "sandbox", "prod", "test":
	default:
		panic(fmt.Errorf("unexpected ENVIRONMENT in .env: %s", stand))
	}

	pass := map[string]any{}
	split := strings.Split(appName, ".")
	namespace := strings.ToLower(split[0])
	microservice := strings.ToLower(split[len(split)-1])

	var opts []func(*api.Config)
	if vaultUrl := os.Getenv("VAULT_URL"); vaultUrl != "" {
		opts = append(opts, func(v *api.Config) {
			v.Address = vaultUrl
		})
	}

	// Vault
	user := os.Getenv("VAULT_USER")
	password := os.Getenv("VAULT_PASSWORD")
	vaultConfig := api.DefaultConfig()
	vaultConfig.Address = "http://vault-ui.vault.svc.cluster.local:8200"
	for _, o := range opts {
		o(vaultConfig)
	}
	vaultClient, err := api.NewClient(vaultConfig)
	if err != nil {
		panic(err)
	}

	if stand == "local" {
		secret, err := vaultClient.Logical().Write(fmt.Sprintf("auth/userpass/login/%s", user), map[string]any{
			"password": password,
		})
		if err != nil {
			secret, err = vaultClient.Logical().Write(fmt.Sprintf("auth/ldap/login/%s", user), map[string]any{
				"password": password,
			})
			if err != nil {
				panic(err)
			}
		}
		if secret.Auth == nil {
			panic(fmt.Errorf("nil auth"))
		}
		vaultClient.SetToken(secret.Auth.ClientToken)
		serviceSecret, err := vaultClient.KVv2(namespace).Get(context.Background(), microservice)
		if err != nil {
			panic(err)
		}
		pass = serviceSecret.Data
	} else {
		k8sAuth, err := kubernetes.NewKubernetesAuth(
			namespace,
			kubernetes.WithServiceAccountTokenPath("/var/run/secrets/kubernetes.io/serviceaccount/token"),
		)
		if err != nil {
			panic(err)
		}
		_, err = vaultClient.Auth().Login(context.Background(), k8sAuth)
		if err != nil {
			panic(err)
		}
		serviceSecret, err := vaultClient.KVv2(namespace).Get(context.Background(), microservice)
		if err != nil {
			panic(err)
		}
		pass = serviceSecret.Data
	}

	var config configTransactionService
	var sc sensitiveConfigTransactionService

	b, err := json.Marshal(pass)
	if err != nil {
		panic(err)
	}

	if err := json.Unmarshal(b, &config); err != nil {
		panic(err)
	}
	config.Check()

	if err := json.Unmarshal(b, &sc); err != nil {
		panic(err)
	}
	sc.Check()

	level := sc.LOG_LEVEL
	if level == "" {
		level = "debug"
	}

	// Logger
	zapConfig := zap.NewProductionConfig()
	parseLevel, err := zapcore.ParseLevel(level)
	if err != nil {
		panic(err)
	}
	zapConfig.Level = zap.NewAtomicLevelAt(parseLevel)
	zapConfig.DisableStacktrace = true
	zapConfig.EncoderConfig.TimeKey = "@timestamp"
	zapConfig.EncoderConfig.EncodeTime = zapcore.RFC3339TimeEncoder
	zapConfig.EncoderConfig.CallerKey = "file"
	log, err := zapConfig.Build(zap.AddCallerSkip(1))
	if err != nil {
		panic(err)
	}
	log = log.Named(appName)
	zap.ReplaceGlobals(log)

	postgresReadConnectionsDirty := sc.DATABASE_READ_DSN
	postgresWriteConnectionsDirty := sc.DATABASE_WRITE_DSN

	postgresReadConnections := make([]string, 0, len(postgresReadConnectionsDirty))
	postgresWriteConnections := make([]string, 0, len(postgresWriteConnectionsDirty))

	for i := range postgresReadConnectionsDirty {
		postgresReadConnections = append(postgresReadConnections, fmt.Sprint(postgresReadConnectionsDirty[i]))
	}

	for i := range postgresWriteConnectionsDirty {
		postgresWriteConnections = append(postgresWriteConnections, fmt.Sprint(postgresWriteConnectionsDirty[i]))
	}

	// Mongo
	dsn := sc.MONGO_DSN
	mongoOpts := options.Client().ApplyURI(dsn)
	mongoOpts.Monitor = otelmongo.NewMonitor()
	connectCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	mongoClient, err := mongo.Connect(connectCtx, mongoOpts)
	if err != nil {
		panic("mongo: " + err.Error())
	}
	err = mongoClient.Ping(context.Background(), nil)
	if err != nil {
		panic("mongo: " + err.Error())
	}

	// Redis
	redisAddr := sc.REDIS_DSN
	redisPassword := sc.REDIS_PASSWORD
	var network string
	if strings.HasPrefix(redisAddr, "/") {
	} else {
		network = "tcp"
	}
	redisClient := v9.NewClient(&v9.Options{
		Addr:     redisAddr,
		Password: redisPassword,
		Network:  network,
		Dialer: func(ctx context.Context, network, addr string) (net.Conn, error) {
			return net.Dial(network, addr)
		},
	})
	_, err = redisClient.Ping(context.Background()).Result()
	if err != nil {
		panic(err)
	}
	if err = v91.InstrumentTracing(redisClient); err != nil {
		panic(err)
	}

	// Notifier
	ctx, cancel := context.WithCancel(context.TODO())

	go func() {
		defer cancel()
		quit := make(chan os.Signal, 1)
		signal.Notify(quit, syscall.SIGTERM, syscall.SIGINT)
		select {
		case <-ctx.Done():
		case <-quit:
		}
	}()

	// Jaeger
	url := sc.JAEGER_DSN
	if stand == "local" {
		url = "http://host.docker.internal:14268/api/traces"
	}
	exp, err := jaeger.New(jaeger.WithCollectorEndpoint(jaeger.WithEndpoint(url)))
	if err != nil {
		panic(err)
	}
	tp := trace.NewTracerProvider(
		trace.WithBatcher(exp),
		trace.WithResource(resource.NewWithAttributes(
			v1_7_0.SchemaURL,
			v1_7_0.ServiceNameKey.String(appName),
			v1_7_0.DeploymentEnvironmentKey.String(stand),
		)),
		trace.WithSpanProcessor(otel.NewSentrySpanProcessor()),
	)
	otel1.SetTracerProvider(tp)
	otel1.SetTextMapPropagator(
		propagation.NewCompositeTextMapPropagator(
			otel.NewSentryPropagator(),
			&propagation.TraceContext{},
			&propagation.Baggage{},
		),
	)

	// Sentry
	dsnSentry := sc.SENTRY_DSN
	if dsnSentry == "" {
		panic(fmt.Errorf("sentry dsn is empty"))
	}
	err = sentry_go.Init(sentry_go.ClientOptions{
		Dsn:              dsnSentry,
		AttachStacktrace: true,
		ServerName:       microservice,
		Environment:      stand,
		EnableTracing:    true,
		TracesSampleRate: 1.0,
	})
	if err != nil {
		panic(err)
	}

	// NatsJSClient
	natsConn, err := nats_go.Connect(sc.NATS_DSN, nats_go.DrainTimeout(10))
	if err != nil {
		panic(fmt.Errorf("error connecting to NATS: %w", err))
	}
	jetStreamContext, err := natsConn.JetStream()
	if err != nil {
		panic(fmt.Errorf("error connect to JetStream: %w", err))
	}
	middlewareLogger := newZapLoggerAdapterTransactionService(log)
	natsMessageRouter, err := message.NewRouter(message.RouterConfig{}, middlewareLogger)
	if err != nil {
		panic(fmt.Errorf("error of create routing: %w", err))
	}
	natsMessageRouter.AddMiddleware(
		middleware.Recoverer,
		func(h message.HandlerFunc) message.HandlerFunc {
			return func(msg *message.Message) ([]*message.Message, error) {
				messages, err := h(msg)
				if err == nil {
					return messages, nil
				}
				msgCtx := msg.Context()
				hub := sentry_go.GetHubFromContext(msgCtx)
				if hub == nil {
					hub = sentry_go.CurrentHub().Clone()
					msgCtx = sentry_go.SetHubOnContext(msgCtx, hub)
				}
				msg.SetContext(msgCtx)
				defer func() {
					if rcv := recover(); rcv != nil {
						recoveredCtx := msg.Context()
						if recoveredCtx != nil {
							ctx = recoveredCtx
						}
						eventID := hub.RecoverWithContext(ctx, rcv)
						if eventID != nil {
							hub.Flush(time.Second)
						}
						panic(rcv)
					}
				}()
				var eventMap map[string]any
				extras := make(map[string]interface{})
				if eventMap != nil {
					extras["raw"] = string(msg.Payload)
					extras["event_uuid"] = msg.UUID
				}
				return messages, err
			}
		},
		func(h message.HandlerFunc) message.HandlerFunc {
			return func(msg *message.Message) ([]*message.Message, error) {
				parentCtx := otel1.GetTextMapPropagator().Extract(msg.Context(), propagation.MapCarrier(msg.Metadata))
				handlerNameFromCtx := message.HandlerNameFromCtx(ctx)
				tracedCtx, span := otel1.Tracer("consumer").Start(
					parentCtx,
					handlerNameFromCtx,
					trace1.WithSpanKind(trace1.SpanKindConsumer),
				)
				defer span.End()
				span.SetAttributes(
					v1_7_0.MessagingDestinationKindTopic,
					v1_7_0.MessagingDestinationKey.String(message.SubscribeTopicFromCtx(tracedCtx)),
					v1_7_0.MessagingOperationReceive,
				)
				msg.SetContext(tracedCtx)
				messages, err := h(msg)
				span.SetAttributes(
					attribute.Bool("error", err != nil),
					attribute.String("nats_uuid", msg.UUID),
				)
				span.RecordError(err)
				return messages, err
			}
		},
	)

	// HealthCheckStore
	healthCheckStore := newHealthCheckStoreTransactionService()

	go func(s *healthCheckStoreTransactionService, ctx context.Context) {
		timer := time.NewTimer(s.timeOut)
		defer timer.Stop()
		for {
			select {
			case <-ctx.Done():
				return
			case <-timer.C:
			}
			func() {
				var errorSlice []error
				if s == nil {
					return
				}
				s.mu.RLock()
				checks := make([]checkTransactionService, len(s.checks))
				copy(checks, s.checks)
				s.mu.RUnlock()
				for _, check := range checks {
					errorSlice = append(errorSlice, check(ctx))
				}
				s.mu.Lock()
				s.err = errors.Join(errorSlice...)
				s.mu.Unlock()
			}()
			timer.Reset(s.timeOut)
		}
	}(healthCheckStore, ctx)

	func(s *healthCheckStoreTransactionService) {
		if s == nil {
			return
		}
		s.mu.Lock()
		defer s.mu.Unlock()
		s.checks = append(s.checks, func(_ context.Context) error {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
				return nil
			}
		})
	}(healthCheckStore)

	// Prometheus
	registry := prometheus.NewRegistry()
	registry.MustRegister(collectors.NewGoCollector())
	registry.MustRegister(collectors.NewProcessCollector(collectors.ProcessCollectorOpts{}))
	registry.MustRegister(collectors.NewBuildInfoCollector())

	// GIN
	ignoreEndpoint := func(endpoint string) bool {
		for _, item := range [4]string{
			"/actuator/prometheus",
			"/ping",
			"/liveness",
			"/readiness",
		} {
			if item == endpoint {
				return true
			}
		}
		return false
	}
	ginEngine := gin.New()
	ginEngine.ContextWithFallback = true
	ginEngine.Use(
		sentrygin.New(sentrygin.Options{
			Repanic:         true,
			WaitForDelivery: true,
			Timeout:         5 * time.Second,
		}),
		otelgin.Middleware(microservice, otelgin.WithFilter(
			func(g *http1.Request) bool {
				return !ignoreEndpoint(g.URL.Path)
			},
		)),
		func(c *gin.Context) {
			var requestBody []byte
			if c.Request.Body != nil {
				requestBody, _ = io.ReadAll(c.Request.Body)
				c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
			}
			defer func() {
				if err := recover(); err != nil {
					httpRequest, _ := httputil.DumpRequest(c.Request, true)

					ctxzap.Error(c.Request.Context(), "[recovery from panic]",
						zap.String("error", fmt.Sprintf("%v", err)),
						zap.String("stack_trace", string(debug.Stack())),
						zap.String("request", string(httpRequest)),
						zap.String("request_body", string(requestBody)),
						zap.Time("time", time.Now()),
					)
					c.JSON(http1.StatusInternalServerError, gin.H{
						"status":      false,
						"status_code": http1.StatusInternalServerError,
						"message":     "Internal Server Error",
						"result":      struct{}{},
					})
					c.Abort()
				}
			}()
			c.Next()
		},
		func(c *gin.Context) {
			ginCtx := context.WithValue(c.Request.Context(), "user_ip", c.Request.Header.Get("X-Forwarded-For"))
			c.Request = c.Request.WithContext(ginCtx)
			c.Next()
		},
	)
	ginEngine.Use(func(c *gin.Context) {
		c.Request = c.Request.WithContext(
			ctxzap.ToContext(
				cntx.NewCounter(c.Request.Context()),
				zap.L().Named(c.Request.URL.String()),
			),
		)
		span := trace1.SpanFromContext(c.Request.Context())
		traceId := span.SpanContext().TraceID().String()
		ctxzap.AddFields(c.Request.Context(), zap.String("traceId", traceId))
		crw := &customResponseWriterTransactionService{
			ResponseWriter: c.Writer,
			Body:           bytes.NewBufferString(""),
		}
		c.Writer = crw
		c.Next()
		status := c.Writer.Status()
		if status < http1.StatusBadRequest {
			if ignoreEndpoint(c.Request.URL.Path) {
				return
			}
			ctxzap.Debug(c.Request.Context(), "correct_ended",
				zap.String("response_body", crw.Body.String()),
			)
		} else {
			var reqBody []byte
			if c.Request.Body != nil {
				reqBody, _ = io.ReadAll(c.Request.Body)
				c.Request.Body = io.NopCloser(bytes.NewBuffer(reqBody))
			}
			ctxzap.Error(c.Request.Context(), "wrong_ended",
				zap.String("response_body", crw.Body.String()),
				zap.String("request_body", string(reqBody)),
			)
		}
	})
	httpMetrics := promhttp.HandlerFor(registry, promhttp.HandlerOpts{})
	ginEngine.GET("/actuator/prometheus", func(c *gin.Context) {
		httpMetrics.ServeHTTP(c.Writer, c.Request)
	})
	ginEngine.GET("/ping", func(c *gin.Context) {
		ctxzap.Debug(c, "healthCheck_ping")
		c.JSON(http1.StatusOK, map[string]string{"message": "pong"})
	})
	ginEngine.GET("/liveness", func(c *gin.Context) {
		if err := healthCheckStore.Status(); err != nil {
			ctxzap.Error(c, "healthCheck_liveness", zap.Error(err))
			c.JSON(http1.StatusServiceUnavailable, map[string]string{"message": err.Error()})
		} else {
			c.JSON(http1.StatusOK, map[string]string{"message": "ok"})
		}
	})
	ginEngine.GET("/readiness", func(c *gin.Context) {
		if err := healthCheckStore.Status(); err != nil {
			ctxzap.Error(c, "healthCheck_readiness", zap.Error(err))
			c.JSON(http1.StatusServiceUnavailable, map[string]string{"message": err.Error()})
		} else {
			c.JSON(http1.StatusOK, map[string]string{"message": "ok"})
		}
	})

	// GRPC
	grpcMetrics := prometheus1.NewServerMetrics(
		prometheus1.WithServerHandlingTimeHistogram(
			prometheus1.WithHistogramBuckets([]float64{0.001, 0.01, 0.1, 0.3, 0.6, 1, 3, 6, 9, 20, 30, 60, 90, 120}),
		),
	)
	registry.MustRegister(grpcMetrics)
	spanProm := func(ctx context.Context) prometheus.Labels {
		if span := trace1.SpanContextFromContext(ctx); span.IsSampled() {
			return prometheus.Labels{"traceId": span.TraceID().String()}
		}
		return nil
	}
	unaryLogInterceptor := func(ctx context.Context, req any, info *grpc1.UnaryServerInfo, handler grpc1.UnaryHandler) (resp any, err error) {
		ctx = ctxzap.ToContext(cntx.NewCounter(ctx), zap.L().Named(info.FullMethod))
		span := trace1.SpanFromContext(ctx)
		ctxzap.AddFields(ctx, zap.String("traceId", span.SpanContext().TraceID().String()))
		resp, err = handler(ctx, req)
		if err != nil {
			ctxzap.Error(ctx, "error_ended", zap.Error(err))
		} else {
			ctxzap.Debug(ctx, "correct_ended")
		}
		return resp, err
	}
	options := []grpc1.ServerOption{
		grpc1.StatsHandler(otelgrpc.NewServerHandler()),
		grpc1.ChainUnaryInterceptor(
			errors1.UnaryServerInterceptor(),
			grpcMetrics.UnaryServerInterceptor(prometheus1.WithExemplarFromContext(spanProm)),
		),
		grpc1.UnaryInterceptor(go_grpc_middleware.ChainUnaryServer(
			tags.UnaryServerInterceptor(),
			unaryLogInterceptor,
			grpc_middleware_sentry.UnaryServerInterceptor(),
		)),
		grpc1.StreamInterceptor(go_grpc_middleware.ChainStreamServer(
			tags.StreamServerInterceptor(),
			grpc_middleware_sentry.StreamServerInterceptor(),
		)),
	}

	grpcServer := grpc1.NewServer(options...)

	// GracefulShutdown
	go func() {
		select {
		case <-ctx.Done():
			grpcServer.GracefulStop()
		}
	}()

	// PostgresClient
	if len(postgresWriteConnections) == 0 {
		panic("postgresWriteConnections database connections is empty")
	}

	gormDB, err := gorm.Open(
		postgres.New(postgres.Config{
			DSN:                  postgresWriteConnections[0],
			PreferSimpleProtocol: true,
		}),
		&gorm.Config{},
	)
	if err != nil {
		panic(err)
	}

	if len(sc.DATABASE_WRITE_DSN) > 1 || len(sc.DATABASE_READ_DSN) > 0 {
		zap.L().Debug("using multiple replica-set")

		sc.DATABASE_WRITE_DSN = sc.DATABASE_WRITE_DSN[1:]
		sourceConnections := make([]gorm.Dialector, len(sc.DATABASE_WRITE_DSN))
		replicaConnections := make([]gorm.Dialector, len(sc.DATABASE_READ_DSN))

		for i, sourceDSN := range sc.DATABASE_WRITE_DSN {
			sourceConnections[i] = postgres.Open(sourceDSN)
		}

		for i, replicaDSN := range sc.DATABASE_READ_DSN {
			replicaConnections[i] = postgres.Open(replicaDSN)
		}

		err = gormDB.Use(dbresolver.Register(
			dbresolver.Config{
				Sources:  sourceConnections,
				Replicas: replicaConnections,
				Policy:   dbresolver.RandomPolicy{},
			},
		))

		if err != nil {
			panic(err)
		}
	}

	connection, err := gormDB.DB()
	if err != nil {
		panic(err)
	}

	connection.SetConnMaxLifetime(time.Hour)
	connection.SetMaxIdleConns(15)
	connection.SetMaxOpenConns(15)

	if err = gormDB.Use(tracing.NewPlugin()); err != nil {
		panic(err)
	}

	return ctx, cancel, ginEngine, gormDB, mongoClient, natsMessageRouter, redisClient, jetStreamContext, natsConn, stand, healthCheckStore, config, microservice, grpcServer
}

type zapLoggerAdapterTransactionService struct {
	logger *zap.Logger
}

func newZapLoggerAdapterTransactionService(logger *zap.Logger) *zapLoggerAdapterTransactionService {
	return &zapLoggerAdapterTransactionService{logger: logger}
}

func (z *zapLoggerAdapterTransactionService) Error(msg string, err error, fields watermill.LogFields) {
	z.logger.Error(msg, append(z.toZapFields(fields), zap.Error(err))...)
}

func (z *zapLoggerAdapterTransactionService) Info(msg string, fields watermill.LogFields) {
	z.logger.Info(msg, z.toZapFields(fields)...)
}

func (z *zapLoggerAdapterTransactionService) Debug(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...)
}

func (z *zapLoggerAdapterTransactionService) Trace(msg string, fields watermill.LogFields) {
	z.logger.Debug(msg, z.toZapFields(fields)...) // Trace maps to Debug
}

func (z *zapLoggerAdapterTransactionService) With(fields watermill.LogFields) watermill.LoggerAdapter {
	newLogger := z.logger.With(z.toZapFields(fields)...)
	return &zapLoggerAdapterTransactionService{logger: newLogger}
}

func (z *zapLoggerAdapterTransactionService) toZapFields(fields watermill.LogFields) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}
	return zapFields
}

type customResponseWriterTransactionService struct {
	gin.ResponseWriter
	Body *bytes.Buffer
}

func (w *customResponseWriterTransactionService) Write(b []byte) (int, error) {
	w.Body.Write(b)
	return w.ResponseWriter.Write(b)
}

type checkTransactionService func(ctx context.Context) error

type healthCheckStoreTransactionService struct {
	timeOut time.Duration
	checks  []checkTransactionService
	err     error
	mu      *sync.RWMutex
}

func newHealthCheckStoreTransactionService() *healthCheckStoreTransactionService {
	return &healthCheckStoreTransactionService{
		timeOut: 10 * time.Second,
		checks:  []checkTransactionService{},
		err:     nil,
		mu:      &sync.RWMutex{},
	}
}

type HealthCheckStoreInterfaceTransactionService interface {
	RegisterCheck(check checkTransactionService)
	Status() error
}

func (s *healthCheckStoreTransactionService) Status() error {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.err
}

func (s *healthCheckStoreTransactionService) RegisterCheck(check checkTransactionService) {
	if s == nil || s.checks == nil {
		return
	}
	s.checks = append(s.checks, check)
}

func registerDBHealthCheckTransactionService(gormDB *gorm.DB, healthCheck *healthCheckStoreTransactionService) {
	db, err := gormDB.DB()
	if err != nil {
		panic(err)
	}
	healthCheck.RegisterCheck(func(ctx context.Context) error {
		if err = db.PingContext(ctx); err != nil {
			return fmt.Errorf("database is not responding: %w", err)
		}
		return nil
	})
}
