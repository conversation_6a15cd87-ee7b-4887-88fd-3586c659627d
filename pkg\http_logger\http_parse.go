package http_logger

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

type HttpLog struct {
	Request      interface{}            `bson:"request"`
	Response     interface{}            `bson:"response"`
	Method       string                 `bson:"method"`
	Url          string                 `bson:"url"`
	HeaderRes    map[string][]string    `bson:"header_res"`
	HeaderReq    map[string][]string    `bson:"header_req"`
	HttpStatus   int                    `bson:"http_status"`
	CtxData      map[string]interface{} `bson:"ctx"`
	OptionalData map[string]interface{} `bson:"optional_data"`
	CreatedAt    time.Time              `bson:"created_at"`
}

func parseResponse(r *http.Response) *HttpLog {
	if r == nil {
		return nil
	}

	var respBuf bytes.Buffer

	var rawResp, rawReq []byte

	var err error

	var method, url string

	respTee := io.TeeReader(r.Body, &respBuf)

	rawResp, err = io.ReadAll(respTee)
	if err != nil {
		return nil
	}

	r.Body = io.NopCloser(&respBuf)

	if r.Request != nil {
		method = r.Request.Method
		url = r.Request.URL.String()

		if r.Request.GetBody != nil {
			requestReader, err := r.Request.GetBody()
			if err != nil {
				fmt.Println(err)
			}

			rawReq, err = io.ReadAll(requestReader)
			if err != nil {
				rawReq = []byte{}
			}
		}
	}

	return &HttpLog{
		Request:  parseJSON(rawReq),
		Response: parseJSON(rawResp),
		Method:   method,
		Url:      url,
	}
}

type prettyField struct {
	Key       string
	Interface interface{}
}

type prettyFields []prettyField

type invalidPair struct {
	position   int
	key, value interface{}
}

type invalidPairs []invalidPair

const half = 2

func sweetenFields(args []interface{}) prettyFields {
	if len(args) == 0 {
		return nil
	}

	var (
		fields  = make([]prettyField, 0, len(args))
		invalid invalidPairs
	)

	for i := 0; i < len(args); {
		if i == len(args)-1 {
			fields = append(fields, field("ignored", args[i]))
			break
		}

		key, val := args[i], args[i+1]
		if keyStr, ok := key.(string); !ok {
			if cap(invalid) == 0 {
				invalid = make(invalidPairs, 0, len(args)/half)
			}

			invalid = append(invalid, invalidPair{i, key, val})
		} else {
			fields = append(fields, field(keyStr, val))
		}

		i += 2
	}

	if len(invalid) > 0 {
		fields = append(fields, field("invalid", invalid))
	}

	return fields
}

func (f prettyFields) toMap() map[string]interface{} {
	var mapFields = make(map[string]interface{})

	for i := range f {
		mapFields[f[i].Key] = f[i].Interface
	}

	return mapFields
}

func field(
	key string,
	val interface{},
) prettyField {
	return prettyField{
		Key:       key,
		Interface: val,
	}
}

func parseJSON(raw []byte) interface{} {
	var j map[string]interface{}

	if err := json.Unmarshal(raw, &j); err != nil {
		return string(raw)
	}

	return j
}
