// Code generated by MockGen. DO NOT EDIT.
// Source: transaction_info_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockTransactionInfClient is a mock of TransactionInfClient interface.
type MockTransactionInfClient struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionInfClientMockRecorder
}

// MockTransactionInfClientMockRecorder is the mock recorder for MockTransactionInfClient.
type MockTransactionInfClientMockRecorder struct {
	mock *MockTransactionInfClient
}

// NewMockTransactionInfClient creates a new mock instance.
func NewMockTransactionInfClient(ctrl *gomock.Controller) *MockTransactionInfClient {
	mock := &MockTransactionInfClient{ctrl: ctrl}
	mock.recorder = &MockTransactionInfClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionInfClient) EXPECT() *MockTransactionInfClientMockRecorder {
	return m.recorder
}

// GetTransactionsWithEmptyBankReferenceID mocks base method.
func (m *MockTransactionInfClient) GetTransactionsWithEmptyBankReferenceID(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.GetTransactionsWithEmptyBankReferenceIDResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionsWithEmptyBankReferenceID", varargs...)
	ret0, _ := ret[0].(*grpc.GetTransactionsWithEmptyBankReferenceIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionsWithEmptyBankReferenceID indicates an expected call of GetTransactionsWithEmptyBankReferenceID.
func (mr *MockTransactionInfClientMockRecorder) GetTransactionsWithEmptyBankReferenceID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsWithEmptyBankReferenceID", reflect.TypeOf((*MockTransactionInfClient)(nil).GetTransactionsWithEmptyBankReferenceID), varargs...)
}

// UpdateBankReferenceID mocks base method.
func (m *MockTransactionInfClient) UpdateBankReferenceID(ctx context.Context, in *grpc.UpdateBankReferenceIDRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateBankReferenceID", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBankReferenceID indicates an expected call of UpdateBankReferenceID.
func (mr *MockTransactionInfClientMockRecorder) UpdateBankReferenceID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBankReferenceID", reflect.TypeOf((*MockTransactionInfClient)(nil).UpdateBankReferenceID), varargs...)
}

// UpdateBankResponseMessage mocks base method.
func (m *MockTransactionInfClient) UpdateBankResponseMessage(ctx context.Context, in *grpc.UpdateBankResponseMessageRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateBankResponseMessage", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBankResponseMessage indicates an expected call of UpdateBankResponseMessage.
func (mr *MockTransactionInfClientMockRecorder) UpdateBankResponseMessage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBankResponseMessage", reflect.TypeOf((*MockTransactionInfClient)(nil).UpdateBankResponseMessage), varargs...)
}

// UpdateJobsMessage mocks base method.
func (m *MockTransactionInfClient) UpdateJobsMessage(ctx context.Context, in *grpc.UpdateJobsMessageRequestV1, opts ...grpc0.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateJobsMessage", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateJobsMessage indicates an expected call of UpdateJobsMessage.
func (mr *MockTransactionInfClientMockRecorder) UpdateJobsMessage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateJobsMessage", reflect.TypeOf((*MockTransactionInfClient)(nil).UpdateJobsMessage), varargs...)
}

// MockTransactionInfServer is a mock of TransactionInfServer interface.
type MockTransactionInfServer struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionInfServerMockRecorder
}

// MockTransactionInfServerMockRecorder is the mock recorder for MockTransactionInfServer.
type MockTransactionInfServerMockRecorder struct {
	mock *MockTransactionInfServer
}

// NewMockTransactionInfServer creates a new mock instance.
func NewMockTransactionInfServer(ctrl *gomock.Controller) *MockTransactionInfServer {
	mock := &MockTransactionInfServer{ctrl: ctrl}
	mock.recorder = &MockTransactionInfServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionInfServer) EXPECT() *MockTransactionInfServerMockRecorder {
	return m.recorder
}

// GetTransactionsWithEmptyBankReferenceID mocks base method.
func (m *MockTransactionInfServer) GetTransactionsWithEmptyBankReferenceID(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.GetTransactionsWithEmptyBankReferenceIDResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionsWithEmptyBankReferenceID", arg0, arg1)
	ret0, _ := ret[0].(*grpc.GetTransactionsWithEmptyBankReferenceIDResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionsWithEmptyBankReferenceID indicates an expected call of GetTransactionsWithEmptyBankReferenceID.
func (mr *MockTransactionInfServerMockRecorder) GetTransactionsWithEmptyBankReferenceID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionsWithEmptyBankReferenceID", reflect.TypeOf((*MockTransactionInfServer)(nil).GetTransactionsWithEmptyBankReferenceID), arg0, arg1)
}

// UpdateBankReferenceID mocks base method.
func (m *MockTransactionInfServer) UpdateBankReferenceID(arg0 context.Context, arg1 *grpc.UpdateBankReferenceIDRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBankReferenceID", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBankReferenceID indicates an expected call of UpdateBankReferenceID.
func (mr *MockTransactionInfServerMockRecorder) UpdateBankReferenceID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBankReferenceID", reflect.TypeOf((*MockTransactionInfServer)(nil).UpdateBankReferenceID), arg0, arg1)
}

// UpdateBankResponseMessage mocks base method.
func (m *MockTransactionInfServer) UpdateBankResponseMessage(arg0 context.Context, arg1 *grpc.UpdateBankResponseMessageRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBankResponseMessage", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBankResponseMessage indicates an expected call of UpdateBankResponseMessage.
func (mr *MockTransactionInfServerMockRecorder) UpdateBankResponseMessage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBankResponseMessage", reflect.TypeOf((*MockTransactionInfServer)(nil).UpdateBankResponseMessage), arg0, arg1)
}

// UpdateJobsMessage mocks base method.
func (m *MockTransactionInfServer) UpdateJobsMessage(arg0 context.Context, arg1 *grpc.UpdateJobsMessageRequestV1) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateJobsMessage", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateJobsMessage indicates an expected call of UpdateJobsMessage.
func (mr *MockTransactionInfServerMockRecorder) UpdateJobsMessage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateJobsMessage", reflect.TypeOf((*MockTransactionInfServer)(nil).UpdateJobsMessage), arg0, arg1)
}

// mustEmbedUnimplementedTransactionInfServer mocks base method.
func (m *MockTransactionInfServer) mustEmbedUnimplementedTransactionInfServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTransactionInfServer")
}

// mustEmbedUnimplementedTransactionInfServer indicates an expected call of mustEmbedUnimplementedTransactionInfServer.
func (mr *MockTransactionInfServerMockRecorder) mustEmbedUnimplementedTransactionInfServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTransactionInfServer", reflect.TypeOf((*MockTransactionInfServer)(nil).mustEmbedUnimplementedTransactionInfServer))
}

// MockUnsafeTransactionInfServer is a mock of UnsafeTransactionInfServer interface.
type MockUnsafeTransactionInfServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTransactionInfServerMockRecorder
}

// MockUnsafeTransactionInfServerMockRecorder is the mock recorder for MockUnsafeTransactionInfServer.
type MockUnsafeTransactionInfServerMockRecorder struct {
	mock *MockUnsafeTransactionInfServer
}

// NewMockUnsafeTransactionInfServer creates a new mock instance.
func NewMockUnsafeTransactionInfServer(ctrl *gomock.Controller) *MockUnsafeTransactionInfServer {
	mock := &MockUnsafeTransactionInfServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTransactionInfServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTransactionInfServer) EXPECT() *MockUnsafeTransactionInfServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTransactionInfServer mocks base method.
func (m *MockUnsafeTransactionInfServer) mustEmbedUnimplementedTransactionInfServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTransactionInfServer")
}

// mustEmbedUnimplementedTransactionInfServer indicates an expected call of mustEmbedUnimplementedTransactionInfServer.
func (mr *MockUnsafeTransactionInfServerMockRecorder) mustEmbedUnimplementedTransactionInfServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTransactionInfServer", reflect.TypeOf((*MockUnsafeTransactionInfServer)(nil).mustEmbedUnimplementedTransactionInfServer))
}
