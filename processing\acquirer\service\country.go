package service

import (
	"context"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/pkg/middlewares"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog"
)

type CountryService struct {
	countryRepo database.Countrier
}

func NewCountryService(
	countryRepo database.Countrier,
) Countrier {
	return &CountryService{
		countryRepo: countryRepo,
	}
}

func (c *CountryService) Create(ctx context.Context, request *schema.Country) (err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryService_Create")
	defer span.End()

	if request == nil {
		return goerr.ErrParseErrorBody
	}

	err = c.countryRepo.Create(ctx, request.ToModel())
	if err != nil {
		return err
	}

	return nil
}

func (c *CountryService) GetAll(
	ctx context.Context,
	pagination *middlewares.PaginationInfo,
) (_ []*model.Country, err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryService_GetAll")
	defer span.End()

	res, err := c.countryRepo.GetAll(ctx, pagination)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (c *CountryService) Update(ctx context.Context, id uint64, request *schema.Country) (err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryService_Update")
	defer span.End()

	if request == nil {
		return goerr.ErrParseErrorBody
	}

	err = c.countryRepo.Update(ctx, id, request.ToModel())
	if err != nil {
		return err
	}

	return nil
}

func (c *CountryService) Delete(ctx context.Context, id uint64) (err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryService_Delete")
	defer span.End()

	err = c.countryRepo.Delete(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

func (c *CountryService) GetByID(ctx context.Context, id uint64) (_ model.Country, err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryService_GetByID")
	defer span.End()

	return c.countryRepo.GetByID(ctx, id)
}

func (c *CountryService) GetCountriesByName(
	ctx context.Context,
	name string,
	pagination *middlewares.PaginationInfo,
) (_ []*model.CountryBasic, err error) {
	ctx, span := dog.CreateSpan(ctx, "CountryService_GetCountriesByName")
	defer span.End()

	return c.countryRepo.GetByName(ctx, name, pagination)
}
