package alatau_city_bank

import (
	"net"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"google.golang.org/grpc/reflection"

	"git.local/sensitive/innerpb/processing/grpc"
	"git.local/sensitive/pkg/encryptor"
	"git.local/sensitive/processing/alatau_city_bank"
	"git.local/sensitive/sdk/dog"
)

var CMD = &cobra.Command{
	Use:       "alatau-city-bank",
	Run:       run,
	ValidArgs: []string{"sandbox"},
}

var (
	sandbox bool
)

func init() {
	CMD.PersistentFlags().BoolVarP(&sandbox, "sandbox", "s", false, "run sandbox")
}

const (
	sentryFlushTimeout = 5 * time.Second
)

func run(cmd *cobra.Command, args []string) {
	defer func() {
		if err, hub := recover(), sentry.CurrentHub(); err != nil && hub != nil {
			hub.Recover(err)
			sentry.Flush(sentryFlushTimeout)
			panic(err)
		}
	}()
	namespase := "processing"

	if sandbox {
		namespase = "sandbox-processing"
	}

	_ = dog.InitApp(namespase + ".alatau_city_bank.alatau_city_bank.Alatau-City-Bank")

	logger := dog.L()

	logger.Info("begin register db healthCheck")
	dog.RegisterDBHealthCheck()
	logger.Info("end register db healthCheck")

	collection := dog.Mongo().Database(dog.MicroService()).Collection("rest_dog")

	decryptManager := encryptor.NewDecryptManager(dog.ConfigString("DECRYPTION_KEY"))

	server := alatau_city_bank.NewAlatauCityBankServer(
		dog.Stand(),
		dog.ConfigString("ACQUIRER_IDENTIFIER"),
		dog.ConfigString("BACKREF"),
		dog.ConfigString("ALATAU_PUBLIC_KEY"),
		collection,
		alatau_city_bank.NewThreeDSTemplateDB(dog.Mongo().Database(dog.MicroService())),
		decryptManager,
	)

	go func() {
		defer dog.Cancel()()

		grpcServer := dog.GrpcServer()

		grpc.RegisterAlatauCityBankServer(
			grpcServer,
			server,
		)

		serverAddr := dog.ConfigString("GRPC_PORT")
		dog.L().Info("serverAdr", zap.String("GRPC_PORT", serverAddr))

		listenerGrpc, err := net.Listen("tcp", ":"+serverAddr)
		if err != nil {
			dog.L().Fatal("can not prepare net.Listener for grpc service", zap.Error(err))
		}

		reflection.Register(grpcServer)

		if err = grpcServer.Serve(listenerGrpc); err != nil {
			dog.L().Fatal("can not run grpc server", zap.Error(err))
		}
	}()

	go func() {
		defer dog.Cancel()()

		serverAddr := dog.ConfigString("HTTP_PORT")
		dog.L().Info("serverAdr", zap.String("HTTP_PORT", serverAddr))

		engine := dog.GinEngine()

		go func() {
			<-time.NewTicker(time.Second).C
			gin.SetMode(gin.ReleaseMode)
		}()

		if err := engine.Run(":" + serverAddr); err != nil {
			logger.Panic("can not run gin engine", zap.Error(err))
		}
	}()

	<-dog.Ctx().Done()
}
