package transaction

import (
	"embed"
	"net"
	"time"

	"git.local/sensitive/innerpb/processing/grpc"
	events "git.local/sensitive/processing/transaction/delivery/event/consumer"

	"github.com/getsentry/sentry-go"
	redis_session "github.com/gin-contrib/sessions/redis"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"google.golang.org/grpc/reflection"

	"git.local/sensitive/pkg/gtransaction"
	"git.local/sensitive/pkg/http_logger"
	_ "git.local/sensitive/processing/account/docs"
	grpcDeliveryV1 "git.local/sensitive/processing/transaction/delivery/grpc/v1"
	httpDelivery "git.local/sensitive/processing/transaction/delivery/http"
	"git.local/sensitive/processing/transaction/docs"
	"git.local/sensitive/processing/transaction/repository"
	"git.local/sensitive/processing/transaction/service"
	"git.local/sensitive/processing/transaction/usecase"
	"git.local/sensitive/sdk/dog"
)

var CMD = &cobra.Command{
	Use: "transaction",
	Run: run,
}

const (
	sentryFlushTimeout = 5 * time.Second
)

//go:generate swag init -g ./../cobra/transaction/service.go -d ./../../transaction --parseDependencyLevel 1 --instanceName Transaction --output ./../../transaction/docs

// //go:embed migrations/*.sql
var embedMigrations embed.FS

// var embedMigrations embed.FS

// @title transaction
// @version 1.0.0
// @description transaction processing
//
// @host api-dev.processing.kz
// @BasePath /transaction
// @schemes https http
// @securityDefinitions.apikey bearerAuth
// @in header
// @name Authorization
func run(cmd *cobra.Command, args []string) {
	defer func() {
		if err, hub := recover(), sentry.CurrentHub(); err != nil && hub != nil {
			hub.Recover(err)
			sentry.Flush(sentryFlushTimeout)
			panic(err)
		}
	}()

	gormDB := dog.InitApp("processing.transaction.transaction.Transaction")

	logger := dog.L()

	logger.Info("begin register db healthCheck")
	dog.RegisterDBHealthCheck()
	logger.Info("end register db healthCheck")

	logger.Info("begin send swagger doc")
	err := dog.InitOpenAPI(docs.SwaggerInfoTransaction)
	if err != nil {
		logger.Warn("error send swagger doc", zap.Error(err))
	}
	logger.Info("end send swagger doc")

	multiAcquiringClient, err := grpc.NewPreparedMultiAcquiringClient()
	if err != nil {
		logger.Panic("error connect to multi-acquiring service", zap.Error(err))
	}
	grpc.NewLoggedMultiAcquiringClient(multiAcquiringClient)

	merchantClient, err := grpc.NewPreparedMerchantClient()
	if err != nil {
		logger.Panic("error connect to merchant service", zap.Error(err))
	}
	grpc.NewLoggedMerchantClient(merchantClient)

	terminalClient, err := grpc.NewPreparedTerminalClient()
	if err != nil {
		logger.Panic("error connect to terminal service", zap.Error(err))
	}
	grpc.NewLoggedTerminalClient(terminalClient)

	cardClient, err := grpc.NewPreparedCardClient()
	if err != nil {
		logger.Panic("error connect to card service", zap.Error(err))
	}
	grpc.NewLoggedCardClient(cardClient)

	smartPayClient, err := grpc.NewPreparedSmartPayClient()
	if err != nil {
		logger.Panic("error connect to smartPay service", zap.Error(err))
	}
	grpc.NewLoggedSmartPayClient(smartPayClient)

	projectTransactionClient, err := grpc.NewPreparedProjectTransactionsClient()
	if err != nil {
		logger.Panic("error connect to project-transaction service", zap.Error(err))
	}
	grpc.NewLoggedProjectTransactionsClient(projectTransactionClient)

	commissionClient, err := grpc.NewPreparedCommissionClient()
	if err != nil {
		logger.Panic("error connect to commission service", zap.Error(err))
	}
	grpc.NewLoggedCommissionClient(commissionClient)

	projectClient, err := grpc.NewPreparedProjectClient()
	if err != nil {
		logger.Panic("error connect to project service", zap.Error(err))
	}
	grpc.NewLoggedProjectClient(projectClient)

	antiFraudClient, err := grpc.NewPreparedCollectorClient()
	if err != nil {
		logger.Panic("error connect to project service", zap.Error(err))
	}
	grpc.NewLoggedCollectorClient(antiFraudClient)

	refundClient, err := grpc.NewPreparedRefundClient()
	if err != nil {
		logger.Panic("error connect to refund service", zap.Error(err))
	}
	grpc.NewLoggedRefundClient(refundClient)

	viewCrafterClient, err := grpc.NewPreparedViewCrafterClient()
	if err != nil {
		logger.Panic("error connect to view-crafter service", zap.Error(err))
	}
	grpc.NewLoggedViewCrafterClient(viewCrafterClient)

	billingClient, err := grpc.NewPreparedBillingClient()
	if err != nil {
		logger.Panic("error connect to billing service", zap.Error(err))
	}
	grpc.NewLoggedBillingClient(billingClient)

	httpLogger := http_logger.NewHttpLogMongo(dog.Mongo().Database(dog.MicroService()), dog.ConfigString("MONGO_COLLECTION"))

	repo := repository.NewRepositories(
		gormDB,
		dog.ConfigString("LIBRE_OFFICE_URL"),
		dog.ConfigInt("BACKOFF_INITIAL_INTERVAL"),
		dog.ConfigFloat("BACKOFF_MULTIPLIER"),
		dog.ConfigInt("BACKOFF_MAX_INTERVAL"),
		dog.ConfigInt("BACKOFF_MAX_ELAPSED_TIME"),
		dog.ConfigFloat("BACKOFF_RANDOMIZATION_FACTOR"),
		httpLogger,
	)

	services := service.NewServices(
		repo,
		gtransaction.NewGormTransactionManager(gormDB),
		dog.ConfigString("DOMAIN"),
		dog.ConfigString("THREEDS_PATH_V1"),
		dog.ConfigString("PAYMENT_VIEW_URL"),
		dog.ConfigString("GOOGLE_PAY_GATEWAY"),
		dog.ConfigString("RECEIPT_HTML_FILE"),
		dog.ConfigString("EMAIL_SENDER"),
		dog.ConfigString("RECEIPT_TITLE"),
		dog.ConfigString("CDN_URL"),
		billingClient,
		viewCrafterClient,
		antiFraudClient,
		cardClient,
		refundClient,
		multiAcquiringClient,
		projectTransactionClient,
		projectClient,
		smartPayClient,
		commissionClient,
		merchantClient,
		terminalClient,
	)

	useCase := usecase.NewUseCase(
		services,
	)

	_, err = events.NewHandler(services)
	if err != nil {
		logger.Panic("cannot create event consumer", zap.Error(err))
		return
	}

	go func() {
		defer dog.Cancel()()

		serverAddr := dog.ConfigString("HTTP_PORT")
		dog.L().Info("serverAdr", zap.String("HTTP_PORT", serverAddr))

		handlerDelivery := httpDelivery.NewHandlerDelivery(
			useCase, dog.MicroService(), dog.Redis(), dog.ConfigString("PAYMENT_VIEW_URL"),
		)

		engine := dog.GinEngine()

		store, sErr := redis_session.NewStoreWithDB( // TODO need to be refactored
			10,
			"tcp",
			dog.ConfigString("REDIS_DSN"),
			dog.ConfigString("REDIS_PASSWORD"),
			"1",
			[]byte(dog.ConfigString("SESSION_KEY")),
		)
		if sErr != nil {
			logger.Panic("cannot create redis connection", zap.Error(sErr))
			return
		}

		handlerDelivery.Init(engine, dog.Stand(), dog.ConfigString("SESSION_SECRET"), store)
		go func() {
			<-time.NewTicker(time.Second).C
			gin.SetMode(gin.ReleaseMode)
		}()

		if err = engine.Run(":" + serverAddr); err != nil {
			logger.Panic("can not run gin engine", zap.Error(err))
		}
	}()

	go func() {
		defer dog.Cancel()()

		grpcServer := dog.GrpcServer()

		grpc.RegisterTransactionServer(
			grpcServer,
			grpc.NewLoggedTransactionServer(grpcDeliveryV1.NewTransactionServer(useCase, services, projectClient, repo.Transaction)),
		)

		grpc.RegisterTransactionCallbackServer(
			grpcServer,
			grpc.NewLoggedTransactionCallbackServer(grpcDeliveryV1.NewTransactionCallbackServer(services)),
		)

		grpc.RegisterTransactionInfServer(
			grpcServer,
			grpc.NewLoggedTransactionInfServer(grpcDeliveryV1.NewTransactionInfoServer(services)),
		)

		grpc.RegisterTransactionStatusServer(
			grpcServer,
			grpc.NewLoggedTransactionStatusServer(grpcDeliveryV1.NewTransactionStatusServer(services)),
		)

		grpc.RegisterTransactionTypeServer(
			grpcServer,
			grpc.NewLoggedTransactionTypeServer(grpcDeliveryV1.NewTransactionTypeServer(services)),
		)

		serverAddr := dog.ConfigString("GRPC_PORT")
		dog.L().Info("serverAdr", zap.String("GRPC_PORT", serverAddr))

		listenerGrpc, err := net.Listen("tcp", ":"+serverAddr)
		if err != nil {
			dog.L().Fatal("can not prepare net.Listener for grpc service", zap.Error(err))
		}

		reflection.Register(grpcServer)

		if err = grpcServer.Serve(listenerGrpc); err != nil {
			dog.L().Fatal("can not run grpc server", zap.Error(err))
		}
	}()

	go func() {
		defer dog.Cancel()()

		dog.L().Info("start nats nats message engine")

		err = dog.NatsMessageRouter().Run(dog.Ctx())
		if err != nil {
			logger.Panic("can not run nats message engine", zap.Error(err))
		}
	}()

	<-dog.Ctx().Done()
}
