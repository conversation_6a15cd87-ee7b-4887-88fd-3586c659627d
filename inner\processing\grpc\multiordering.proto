edition = "2023";

package processing.multiordering.multiordering;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/timestamp.proto";

service Multiordering {
  rpc GetPaymentOrder(GetPaymentOrderRequest) returns (GetPaymentOrderResponse) {}
  rpc GetOrderingIdentifier(GetOrderingIdentifierRequest) returns (GetOrderingIdentifierResponse) {}
}

message GetOrderingIdentifierRequest {}

message GetOrderingIdentifierResponse {
  string identifier = 1;
}

message GetPaymentOrderRequest {
  google.protobuf.Timestamp date = 1;
}

message GetPaymentOrderResponse {
  string external_id = 1;
  repeated SubmerchantData submerchants = 2;

  message SubmerchantData {
    string bin = 1;
    string name = 2;
    string recipient_account = 3;
    string payment_purpose_code = 4;
    string beneficiary_code = 5;
    string description = 6;
    double amount = 7;
  }
}