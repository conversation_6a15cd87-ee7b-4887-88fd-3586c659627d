package test

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	processing "git.local/sensitive/innerpb/processing/events"
	"git.local/sensitive/sdk/dog"
)

func TestNatsPublisher(t *testing.T) {
	t.Skip()

	os.Setenv("ENVIRONMENT", "local")
	os.Setenv("VAULT_URL", "https://vault.dev-tarlanpayments.kz")
	os.Setenv("VAULT_USER", "r.baidildinov")
	os.Setenv("VAULT_PASSWORD", "1Hdf@13em-asLj9DfZZ")
	dog.InitApp("processing.transaction.transaction.transaction")

	err := processing.NewTestMessagePublisher(
		dog.JSContext(),
	)(context.Background(), &processing.TestMessage{
		UserId:   999,
		Card:     "123 123 123 999",
		IsActive: true,
	})
	if err != nil {
		t.Error(err)
	}
}

func TestNatsSubscriber(t *testing.T) {
	t.Skip()

	os.Setenv("ENVIRONMENT", "local")
	os.Setenv("VAULT_URL", "https://vault.dev-tarlanpayments.kz")
	os.Setenv("VAULT_USER", "r.baidildinov")
	os.Setenv("VAULT_PASSWORD", "1Hdf@13em-asLj9DfZZ")
	dog.InitApp("processing.transaction.transaction.transaction")

	chResponse, err := processing.NewTestMessageSubscriber(
		dog.JSContext(),
		dog.NatsConnection(),
		dog.NatsMessageRouter(),
	)(context.Background())
	if err != nil {
		t.Error(err)
	}

	go func() {
		for {
			WorkWithMsg(<-chResponse)
		}
	}()

	go func() {
		err := dog.NatsMessageRouter().Run(context.Background())
		if err != nil {
			t.Error(err)
		}
	}()

	fmt.Println("NatsSubscriber-------1 started")
	time.Sleep(time.Second * 100)

}

func WorkWithMsg(testMessage *processing.TestMessage) {
	fmt.Println("WorkWithMsg-----------@1", testMessage)
}

func WorkWithMsg2(testMessage *processing.TestMessage) {
	fmt.Println("WorkWithMsg-----------@2", testMessage)
}

func TestNatsSubscriber2(t *testing.T) {
	t.Skip()

	os.Setenv("ENVIRONMENT", "local")
	os.Setenv("VAULT_URL", "https://vault.dev-tarlanpayments.kz")
	os.Setenv("VAULT_USER", "r.baidildinov")
	os.Setenv("VAULT_PASSWORD", "1Hdf@13em-asLj9DfZZ")
	dog.InitApp("processing.transaction.transaction.transaction")

	handler := processing.NewTestMessageSubscriber(dog.JSContext(), dog.NatsConnection(), dog.NatsMessageRouter())

	chResponse, err := handler(context.Background())
	if err != nil {
		t.Error(err)
	}

	go func() {
		for {
			testMessage := <-chResponse
			WorkWithMsg2(testMessage)
		}
	}()
	go func() {
		err := dog.NatsMessageRouter().Run(context.Background())
		if err != nil {
			t.Error(err)
		}
	}()
	fmt.Println("NatsSubscriber------2 started")
	time.Sleep(time.Second * 100)

}
