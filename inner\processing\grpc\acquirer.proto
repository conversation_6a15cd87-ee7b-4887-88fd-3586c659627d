edition = "2023";

package processing.acquirer.acquirer;

option go_package = "git.local/sensitive/innerpb/processing/grpc";

import "google/protobuf/empty.proto";

service Acquirer {
  rpc GetAcquirerByID(GetAcquirerByIDRequestV1) returns(GetAcquirerByIDResponseV1) {}
  rpc GetIssuerByID(GetIssuerByIDRequestV1) returns(GetIssuerByIDResponseV1) {}
  rpc GetIpsByID(GetIpsByIDRequestV1) returns(GetIpsByIDResponseV1) {}
  rpc GetCountryByID(GetCountryByIDRequestV1) returns(GetCountryByIDResponseV1) {}
  rpc GetAcquirers(google.protobuf.Empty) returns(GetAcquirersV1) {}
  rpc GetBankByID(GetBankByIDRequestV1) returns(GetBankByIDResponseV1) {}
}

message GetBankByIDRequestV1 {
  uint64 bank_id = 1;
}

message GetBankByIDResponseV1 {
  uint64 id = 1;
  string name = 2;
}

message GetAcquirerByIDRequestV1 {
  uint64 acquirer_id = 1;
}

message GetAcquirerByIDResponseV1 {
  uint64 id = 1;
  string code = 2;
  uint64 bank_id = 3;
  string contract = 4;
  uint64 country_id = 5;
  string description = 6;
  string name = 7;
}

message GetIssuerByIDRequestV1 {
  uint64 issuer_id = 1;
}

message GetIssuerByIDResponseV1 {
  uint64 id = 1;
  string name = 2;
  string bik = 3;
  string swift = 4;
}

message GetIpsByIDRequestV1 {
  uint64 ips_id = 1;
}

message GetIpsByIDResponseV1 {
  uint64 id = 1;
  string name = 2;
}

message GetCountryByIDRequestV1 {
  uint64 country_id = 1;
}

message GetCountryByIDResponseV1 {
  uint64 id = 1;
  string name = 2;
}

message GetAcquirersV1 {
  repeated GetAcquirerByIDResponseV1 acquirer = 1;
}