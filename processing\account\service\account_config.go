package service

import (
	"context"
	"encoding/json"
	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/account/repository"
	"git.local/sensitive/processing/account/schema"
	"git.local/sensitive/sdk/dog"
)

type AccountConfigService struct {
	accountInfoRepo    repository.AccountInformer
	accountUpdaterRepo repository.AccountUpdater
}

func NewAccountConfigService(
	accountInfoRepo repository.AccountInformer,
	accountUpdaterRepo repository.AccountUpdater,
) AccountConfiger {
	return &AccountConfigService{
		accountInfoRepo:    accountInfoRepo,
		accountUpdaterRepo: accountUpdaterRepo,
	}
}

func (ac *AccountConfigService) GetAccountConfig(
	ctx context.Context,
	id uint64,
) (decryptedConfig schema.DecryptedAccountConfig, err error) {
	ctx, span := dog.CreateSpan(ctx, "AccountConfigService_GetAccountConfig")
	defer span.End()

	account, err := ac.accountInfoRepo.GetById(ctx, id)
	if err != nil {
		return nil, err
	}

	decrypted, err := dog.AESDecrypt(account.EncryptedConfig, dog.ConfigString("SYMMETRIC_KEY"))
	if err != nil {
		return nil, err
	}

	if err = json.Unmarshal([]byte(decrypted), &decryptedConfig); err != nil {
		return nil, err
	}

	return decryptedConfig, nil
}

func (ac *AccountConfigService) UpdateAccountConfig(ctx context.Context, id uint64, config map[string]any) (err error) {
	ctx, span := dog.CreateSpan(ctx, "AccountConfigService_UpdateAccountConfig")
	defer span.End()

	jsonConfig, err := json.Marshal(config)
	if err != nil {
		return goerr.ErrTerminalConfigEncryption.WithErr(err).WithCtx(ctx)
	}

	encryptedConfig, err := dog.AESEncrypt(string(jsonConfig), dog.ConfigString("SYMMETRIC_KEY"))
	if err != nil {
		return goerr.ErrTerminalConfigEncryption.WithErr(err).WithCtx(ctx)
	}

	return ac.accountUpdaterRepo.UpdateAccountConfig(ctx, id, encryptedConfig)
}
