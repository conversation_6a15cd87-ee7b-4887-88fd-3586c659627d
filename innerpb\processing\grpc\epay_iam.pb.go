// Code generated by protoc-gen-go-mvp. DO NOT EDIT.
// versions:
// - protoc-gen-go-mvp v2.0.1
// created by mvp authors

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

func NewIamEpayServer(
	srv EpayServer,
) EpayServer {
	return &iamEpayServer{
		srv: srv,
	}
}

var _ EpayServer = (*iamEpayServer)(nil)

type iamEpayServer struct {
	UnimplementedEpayServer

	srv EpayServer
}

func (s *iamEpayServer) PayIn(
	ctx context.Context,
	req *PayInRequestData,
) (
	*PayInResponseData,
	error,
) {
	return s.srv.PayIn(ctx, req)
}

func (s *iamEpayServer) OneClickPayIn(
	ctx context.Context,
	req *OneClickPayInRequestData,
) (
	*PayInResponseData,
	error,
) {
	return s.srv.OneClickPayIn(ctx, req)
}

func (s *iamEpayServer) ThreeDSConfirm(
	ctx context.Context,
	req *ThreeDSRequestData,
) (
	*ThreeDSResponseData,
	error,
) {
	return s.srv.ThreeDSConfirm(ctx, req)
}

func (s *iamEpayServer) ThreeDSResume(
	ctx context.Context,
	req *ThreeDSResumeRequest,
) (
	*ThreeDSResumeResponse,
	error,
) {
	return s.srv.ThreeDSResume(ctx, req)
}

func (s *iamEpayServer) PayOut(
	ctx context.Context,
	req *PayOutRequestData,
) (
	*PayOutResponseData,
	error,
) {
	return s.srv.PayOut(ctx, req)
}

func (s *iamEpayServer) GetBankTransactionStatus(
	ctx context.Context,
	req *BankTransactionStatusRequest,
) (
	*BankTransactionStatusResponse,
	error,
) {
	return s.srv.GetBankTransactionStatus(ctx, req)
}

func (s *iamEpayServer) GetBankTransactionStatusUnformated(
	ctx context.Context,
	req *BankTransactionStatusUnformatedRequest,
) (
	*BankTransactionStatusUnformatedResponse,
	error,
) {
	return s.srv.GetBankTransactionStatusUnformated(ctx, req)
}

func (s *iamEpayServer) Refund(
	ctx context.Context,
	req *RefundRequest,
) (
	*RefundResponse,
	error,
) {
	return s.srv.Refund(ctx, req)
}

func (s *iamEpayServer) GooglePay(
	ctx context.Context,
	req *GooglePayRequestData,
) (
	*GooglePayResponseData,
	error,
) {
	return s.srv.GooglePay(ctx, req)
}

func (s *iamEpayServer) ApplePay(
	ctx context.Context,
	req *ApplePayRequestData,
) (
	*ApplePayResponseData,
	error,
) {
	return s.srv.ApplePay(ctx, req)
}

func (s *iamEpayServer) TwoStagePayIn(
	ctx context.Context,
	req *TwoStagePayInRequest,
) (
	*TwoStagePayInResponse,
	error,
) {
	return s.srv.TwoStagePayIn(ctx, req)
}

func (s *iamEpayServer) Charge(
	ctx context.Context,
	req *ChargeRequest,
) (
	*ChargeResponse,
	error,
) {
	return s.srv.Charge(ctx, req)
}

func (s *iamEpayServer) Cancel(
	ctx context.Context,
	req *CancelRequest,
) (
	*CancelResponse,
	error,
) {
	return s.srv.Cancel(ctx, req)
}

func (s *iamEpayServer) MakeToken(
	ctx context.Context,
	req *PayInRequestData,
) (
	*PayInResponseData,
	error,
) {
	return s.srv.MakeToken(ctx, req)
}

func (s *iamEpayServer) GetAcquirerIdentifier(
	ctx context.Context,
	req *emptypb.Empty,
) (
	*GetAcquirerIdentifierResponse,
	error,
) {
	return s.srv.GetAcquirerIdentifier(ctx, req)
}

func (s *iamEpayServer) ResolveVisaAlias(
	ctx context.Context,
	req *ResolveVisaAliasRequest,
) (
	*ResolveVisaAliasResponse,
	error,
) {
	return s.srv.ResolveVisaAlias(ctx, req)
}

func (s *iamEpayServer) PayOutByPhone(
	ctx context.Context,
	req *PayOutByPhoneRequestData,
) (
	*PayOutResponseByPhoneData,
	error,
) {
	return s.srv.PayOutByPhone(ctx, req)
}

func NewIamEpayClient(
	client EpayClient,
) EpayClient {
	return &iamEpayClient{
		client: client,
	}
}

type iamEpayClient struct {
	client EpayClient
}

func (s *iamEpayClient) PayIn(
	ctx context.Context,
	req *PayInRequestData,
	opts ...grpc.CallOption,
) (
	*PayInResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.PayIn(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) OneClickPayIn(
	ctx context.Context,
	req *OneClickPayInRequestData,
	opts ...grpc.CallOption,
) (
	*PayInResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.OneClickPayIn(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) ThreeDSConfirm(
	ctx context.Context,
	req *ThreeDSRequestData,
	opts ...grpc.CallOption,
) (
	*ThreeDSResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ThreeDSConfirm(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) ThreeDSResume(
	ctx context.Context,
	req *ThreeDSResumeRequest,
	opts ...grpc.CallOption,
) (
	*ThreeDSResumeResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ThreeDSResume(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) PayOut(
	ctx context.Context,
	req *PayOutRequestData,
	opts ...grpc.CallOption,
) (
	*PayOutResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.PayOut(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) GetBankTransactionStatus(
	ctx context.Context,
	req *BankTransactionStatusRequest,
	opts ...grpc.CallOption,
) (
	*BankTransactionStatusResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetBankTransactionStatus(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) GetBankTransactionStatusUnformated(
	ctx context.Context,
	req *BankTransactionStatusUnformatedRequest,
	opts ...grpc.CallOption,
) (
	*BankTransactionStatusUnformatedResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetBankTransactionStatusUnformated(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) Refund(
	ctx context.Context,
	req *RefundRequest,
	opts ...grpc.CallOption,
) (
	*RefundResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.Refund(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) GooglePay(
	ctx context.Context,
	req *GooglePayRequestData,
	opts ...grpc.CallOption,
) (
	*GooglePayResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GooglePay(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) ApplePay(
	ctx context.Context,
	req *ApplePayRequestData,
	opts ...grpc.CallOption,
) (
	*ApplePayResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ApplePay(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) TwoStagePayIn(
	ctx context.Context,
	req *TwoStagePayInRequest,
	opts ...grpc.CallOption,
) (
	*TwoStagePayInResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.TwoStagePayIn(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) Charge(
	ctx context.Context,
	req *ChargeRequest,
	opts ...grpc.CallOption,
) (
	*ChargeResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.Charge(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) Cancel(
	ctx context.Context,
	req *CancelRequest,
	opts ...grpc.CallOption,
) (
	*CancelResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.Cancel(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) MakeToken(
	ctx context.Context,
	req *PayInRequestData,
	opts ...grpc.CallOption,
) (
	*PayInResponseData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.MakeToken(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) GetAcquirerIdentifier(
	ctx context.Context,
	req *emptypb.Empty,
	opts ...grpc.CallOption,
) (
	*GetAcquirerIdentifierResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.GetAcquirerIdentifier(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) ResolveVisaAlias(
	ctx context.Context,
	req *ResolveVisaAliasRequest,
	opts ...grpc.CallOption,
) (
	*ResolveVisaAliasResponse,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.ResolveVisaAlias(metadata.NewOutgoingContext(ctx, md), req)
}

func (s *iamEpayClient) PayOutByPhone(
	ctx context.Context,
	req *PayOutByPhoneRequestData,
	opts ...grpc.CallOption,
) (
	*PayOutResponseByPhoneData,
	error,
) {
	md, _ := metadata.FromIncomingContext(ctx)
	return s.client.PayOutByPhone(metadata.NewOutgoingContext(ctx, md), req)
}
