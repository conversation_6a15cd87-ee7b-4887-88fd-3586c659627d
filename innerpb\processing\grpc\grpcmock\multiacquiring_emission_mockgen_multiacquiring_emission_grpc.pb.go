// Code generated by MockGen. DO NOT EDIT.
// Source: multiacquiring_emission_grpc.pb.go

// Package grpcmock is a generated GoMock package.
package grpcmock

import (
	context "context"
	reflect "reflect"

	grpc "git.local/sensitive/innerpb/processing/grpc"
	gomock "github.com/golang/mock/gomock"
	grpc0 "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockMultiacquiringEmissionClient is a mock of MultiacquiringEmissionClient interface.
type MockMultiacquiringEmissionClient struct {
	ctrl     *gomock.Controller
	recorder *MockMultiacquiringEmissionClientMockRecorder
}

// MockMultiacquiringEmissionClientMockRecorder is the mock recorder for MockMultiacquiringEmissionClient.
type MockMultiacquiringEmissionClientMockRecorder struct {
	mock *MockMultiacquiringEmissionClient
}

// NewMockMultiacquiringEmissionClient creates a new mock instance.
func NewMockMultiacquiringEmissionClient(ctrl *gomock.Controller) *MockMultiacquiringEmissionClient {
	mock := &MockMultiacquiringEmissionClient{ctrl: ctrl}
	mock.recorder = &MockMultiacquiringEmissionClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMultiacquiringEmissionClient) EXPECT() *MockMultiacquiringEmissionClientMockRecorder {
	return m.recorder
}

// ConfirmEmission mocks base method.
func (m *MockMultiacquiringEmissionClient) ConfirmEmission(ctx context.Context, in *grpc.EmoneyRequest, opts ...grpc0.CallOption) (*grpc.EmoneyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmEmission", varargs...)
	ret0, _ := ret[0].(*grpc.EmoneyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmEmission indicates an expected call of ConfirmEmission.
func (mr *MockMultiacquiringEmissionClientMockRecorder) ConfirmEmission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmEmission", reflect.TypeOf((*MockMultiacquiringEmissionClient)(nil).ConfirmEmission), varargs...)
}

// GetEmission mocks base method.
func (m *MockMultiacquiringEmissionClient) GetEmission(ctx context.Context, in *emptypb.Empty, opts ...grpc0.CallOption) (*grpc.EmissionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEmission", varargs...)
	ret0, _ := ret[0].(*grpc.EmissionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEmission indicates an expected call of GetEmission.
func (mr *MockMultiacquiringEmissionClientMockRecorder) GetEmission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmission", reflect.TypeOf((*MockMultiacquiringEmissionClient)(nil).GetEmission), varargs...)
}

// MockMultiacquiringEmissionServer is a mock of MultiacquiringEmissionServer interface.
type MockMultiacquiringEmissionServer struct {
	ctrl     *gomock.Controller
	recorder *MockMultiacquiringEmissionServerMockRecorder
}

// MockMultiacquiringEmissionServerMockRecorder is the mock recorder for MockMultiacquiringEmissionServer.
type MockMultiacquiringEmissionServerMockRecorder struct {
	mock *MockMultiacquiringEmissionServer
}

// NewMockMultiacquiringEmissionServer creates a new mock instance.
func NewMockMultiacquiringEmissionServer(ctrl *gomock.Controller) *MockMultiacquiringEmissionServer {
	mock := &MockMultiacquiringEmissionServer{ctrl: ctrl}
	mock.recorder = &MockMultiacquiringEmissionServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMultiacquiringEmissionServer) EXPECT() *MockMultiacquiringEmissionServerMockRecorder {
	return m.recorder
}

// ConfirmEmission mocks base method.
func (m *MockMultiacquiringEmissionServer) ConfirmEmission(arg0 context.Context, arg1 *grpc.EmoneyRequest) (*grpc.EmoneyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmEmission", arg0, arg1)
	ret0, _ := ret[0].(*grpc.EmoneyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmEmission indicates an expected call of ConfirmEmission.
func (mr *MockMultiacquiringEmissionServerMockRecorder) ConfirmEmission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmEmission", reflect.TypeOf((*MockMultiacquiringEmissionServer)(nil).ConfirmEmission), arg0, arg1)
}

// GetEmission mocks base method.
func (m *MockMultiacquiringEmissionServer) GetEmission(arg0 context.Context, arg1 *emptypb.Empty) (*grpc.EmissionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEmission", arg0, arg1)
	ret0, _ := ret[0].(*grpc.EmissionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEmission indicates an expected call of GetEmission.
func (mr *MockMultiacquiringEmissionServerMockRecorder) GetEmission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmission", reflect.TypeOf((*MockMultiacquiringEmissionServer)(nil).GetEmission), arg0, arg1)
}

// mustEmbedUnimplementedMultiacquiringEmissionServer mocks base method.
func (m *MockMultiacquiringEmissionServer) mustEmbedUnimplementedMultiacquiringEmissionServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMultiacquiringEmissionServer")
}

// mustEmbedUnimplementedMultiacquiringEmissionServer indicates an expected call of mustEmbedUnimplementedMultiacquiringEmissionServer.
func (mr *MockMultiacquiringEmissionServerMockRecorder) mustEmbedUnimplementedMultiacquiringEmissionServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMultiacquiringEmissionServer", reflect.TypeOf((*MockMultiacquiringEmissionServer)(nil).mustEmbedUnimplementedMultiacquiringEmissionServer))
}

// MockUnsafeMultiacquiringEmissionServer is a mock of UnsafeMultiacquiringEmissionServer interface.
type MockUnsafeMultiacquiringEmissionServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeMultiacquiringEmissionServerMockRecorder
}

// MockUnsafeMultiacquiringEmissionServerMockRecorder is the mock recorder for MockUnsafeMultiacquiringEmissionServer.
type MockUnsafeMultiacquiringEmissionServerMockRecorder struct {
	mock *MockUnsafeMultiacquiringEmissionServer
}

// NewMockUnsafeMultiacquiringEmissionServer creates a new mock instance.
func NewMockUnsafeMultiacquiringEmissionServer(ctrl *gomock.Controller) *MockUnsafeMultiacquiringEmissionServer {
	mock := &MockUnsafeMultiacquiringEmissionServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeMultiacquiringEmissionServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeMultiacquiringEmissionServer) EXPECT() *MockUnsafeMultiacquiringEmissionServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedMultiacquiringEmissionServer mocks base method.
func (m *MockUnsafeMultiacquiringEmissionServer) mustEmbedUnimplementedMultiacquiringEmissionServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMultiacquiringEmissionServer")
}

// mustEmbedUnimplementedMultiacquiringEmissionServer indicates an expected call of mustEmbedUnimplementedMultiacquiringEmissionServer.
func (mr *MockUnsafeMultiacquiringEmissionServerMockRecorder) mustEmbedUnimplementedMultiacquiringEmissionServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMultiacquiringEmissionServer", reflect.TypeOf((*MockUnsafeMultiacquiringEmissionServer)(nil).mustEmbedUnimplementedMultiacquiringEmissionServer))
}
