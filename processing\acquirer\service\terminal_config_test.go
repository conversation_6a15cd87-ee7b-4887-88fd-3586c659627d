package service

import (
	"context"
	"crypto/aes"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.local/sensitive/innerpb/processing/goerr"
	"git.local/sensitive/processing/acquirer/model"
	"git.local/sensitive/processing/acquirer/repository/database/databasemocks"
	"git.local/sensitive/processing/acquirer/schema"
	"git.local/sensitive/sdk/dog/e2e"
)

func TestUpdateConfig(t *testing.T) {
	type updateConfigOp struct {
		isCalled    bool
		inputID     uint64
		inputConfig string
		outputErr   error
	}

	tests := []struct {
		name         string
		reqID        uint64
		reqConfig    map[string]any
		updateConfig updateConfigOp
		appConfig    map[string]any
		wantErr      error
	}{
		{
			name: "json umnarshal error",
			reqConfig: map[string]any{
				"some-param": make(chan any),
			},
			reqID: 23,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			wantErr: goerr.ErrTerminalConfigEncryption,
		},
		{
			name: "encryption error",
			reqConfig: map[string]any{
				"my-name-is": "skrillex",
			},
			reqID: 23,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
			wantErr: goerr.ErrTerminalConfigEncryption,
		},
		{
			name: "update error",
			reqConfig: map[string]any{
				"my-name-is": "skrillex",
			},
			reqID: 23,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			updateConfig: updateConfigOp{
				isCalled:    true,
				inputConfig: defaultEncryptedConfig,
				inputID:     23,
				outputErr:   goerr.ErrDbUnexpected,
			},
			wantErr: goerr.ErrDbUnexpected,
		},
		{
			name: "success",
			reqConfig: map[string]any{
				"my-name-is": "skrillex",
			},
			reqID: 23,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			updateConfig: updateConfigOp{
				isCalled:    true,
				inputConfig: defaultEncryptedConfig,
				inputID:     23,
				outputErr:   nil,
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			terminalDBMock := databasemocks.NewMockTerminaler(ctrl)

			s := NewTerminalConfigService(terminalDBMock, nil)

			if tt.updateConfig.isCalled {
				terminalDBMock.EXPECT().UpdateConfig(
					gomock.Any(),
					tt.updateConfig.inputID,
					tt.updateConfig.inputConfig,
				).Return(tt.updateConfig.outputErr)
			}

			err := s.UpdateConfig(context.Background(), tt.reqID, tt.reqConfig)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorIs(t, err, tt.wantErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestGetConfig(t *testing.T) {
	type getAllInfoOp struct {
		inputID   uint64
		output    *model.Terminal
		outputErr error
	}

	tests := []struct {
		name       string
		reqID      uint64
		want       schema.DecryptedConfig
		wantErr    error
		appConfig  map[string]any
		getAllInfo getAllInfoOp
	}{
		{
			name:    "get all info error",
			reqID:   22,
			want:    nil,
			wantErr: goerr.ErrDbUnexpected,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			getAllInfo: getAllInfoOp{
				inputID:   22,
				output:    nil,
				outputErr: goerr.ErrDbUnexpected,
			},
		},
		{
			name:    "decryption error",
			reqID:   22,
			want:    nil,
			wantErr: aes.KeySizeError(3),
			appConfig: map[string]any{
				"SYMMETRIC_KEY": badKey,
			},
			getAllInfo: getAllInfoOp{
				inputID: 22,
				output: &model.Terminal{
					ID:                   22,
					AcquirerID:           1,
					Status:               model.TerminalOn,
					EncryptedConfig:      defaultEncryptedConfig,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				outputErr: nil,
			},
		},
		{
			name:  "success",
			reqID: 22,
			want: map[string]any{
				"my-name-is": "skrillex",
			},
			wantErr: nil,
			appConfig: map[string]any{
				"SYMMETRIC_KEY": normalKey,
			},
			getAllInfo: getAllInfoOp{
				inputID: 22,
				output: &model.Terminal{
					ID:                   22,
					AcquirerID:           1,
					Status:               model.TerminalOn,
					EncryptedConfig:      defaultEncryptedConfig,
					TwoStageTimeout:      10,
					AccountNumber:        "********************",
					IsTransit:            false,
					Description:          "some desc",
					AcquirerTerminalName: "test terminal",
				},
				outputErr: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			c := context.Background()
			ctx, cancel := context.WithCancel(c)
			e2e.InitAppE2E(t, ctx, cancel, nil, nil, tt.appConfig)

			terminalBasicMock := databasemocks.NewMockTerminalBasicer(ctrl)

			s := NewTerminalConfigService(nil, terminalBasicMock)

			terminalBasicMock.EXPECT().GetAllInfoByID(
				gomock.Any(),
				tt.getAllInfo.inputID,
			).Return(tt.getAllInfo.output, tt.getAllInfo.outputErr)

			resp, err := s.GetConfig(context.Background(), tt.reqID)
			if tt.wantErr != nil {
				require.Error(t, err)
				require.ErrorIs(t, err, tt.wantErr)
			} else {
				require.NoError(t, err)
			}

			require.Equal(t, tt.want, resp)
		})
	}
}
