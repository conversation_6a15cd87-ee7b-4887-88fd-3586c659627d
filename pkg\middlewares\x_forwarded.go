package middlewares

import (
	"context"

	"github.com/gin-gonic/gin"
)

const UserIPCTX = "user_ip"

// SetUserIP is a middleware that sets the user IP address to the context.
func SetUserIP() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := context.WithValue(c.Request.Context(), UserIPCTX, c.Request.Header.Get("X-Forwarded-For"))
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

func GetUserIP(ctx context.Context) string {
	ip, ok := ctx.Value(UserIPCTX).(string)
	if !ok {
		ip = ""
	}

	return ip
}
